"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n * \r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \nclass CoreManager {\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        try {\n            // 动态导入SimpleMindMap避免SSR问题\n            const { default: SimpleMindMap } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_simple-mind-map_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map */ \"(app-pages-browser)/./node_modules/simple-mind-map/index.js\"));\n            // 清理现有实例\n            if (this.mindMapInstance) {\n                this.mindMapInstance.destroy();\n            }\n            // 确保容器有明确的尺寸\n            const containerRect = this.config.container.getBoundingClientRect();\n            console.log(\"\\uD83D\\uDCD0 容器尺寸:\", {\n                width: containerRect.width,\n                height: containerRect.height\n            });\n            // 合并配置\n            const finalConfig = {\n                ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                ...this.config.config,\n                el: this.config.container,\n                data: this.config.data,\n                readonly: this.config.readonly,\n                // 明确设置画布尺寸\n                width: containerRect.width || 800,\n                height: containerRect.height || 600\n            };\n            // 创建实例\n            this.mindMapInstance = new SimpleMindMap(finalConfig);\n            this.isInitialized = true;\n            console.log(\"✅ SimpleMindMap实例初始化成功\");\n            return this.mindMapInstance;\n        } catch (error) {\n            console.error(\"❌ SimpleMindMap初始化失败:\", error);\n            throw new Error(\"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\"));\n        }\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            this.mindMapInstance.setData(data);\n            console.log(\"✅ 数据设置成功\");\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n            console.log(\"✅ 数据更新成功\");\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n            console.log(\"✅ 画布适应完成\");\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n            console.log(\"✅ 视图重置完成\");\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 销毁实例\r\n   */ destroy() {\n        if (this.mindMapInstance) {\n            try {\n                this.mindMapInstance.destroy();\n                console.log(\"✅ SimpleMindMap实例销毁完成\");\n            } catch (error) {\n                console.error(\"❌ 销毁SimpleMindMap实例失败:\", error);\n            }\n        }\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9Db3JlTWFuYWdlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7Ozs7OztDQVFDLEdBRW9HO0FBUzlGLE1BQU1DO0lBU1g7O0dBRUMsR0FDRCxNQUFNQyxhQUE2QztRQUNqRCxJQUFJO1lBQ0YsMkJBQTJCO1lBQzNCLE1BQU0sRUFBRUMsU0FBU0MsYUFBYSxFQUFFLEdBQUcsTUFBTSwyT0FBTztZQUVoRCxTQUFTO1lBQ1QsSUFBSSxJQUFJLENBQUNDLGVBQWUsRUFBRTtnQkFDeEIsSUFBSSxDQUFDQSxlQUFlLENBQUNDLE9BQU87WUFDOUI7WUFFQSxhQUFhO1lBQ2IsTUFBTUMsZ0JBQWdCLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxTQUFTLENBQUNDLHFCQUFxQjtZQUNqRUMsUUFBUUMsR0FBRyxDQUFDLHNCQUFZO2dCQUN0QkMsT0FBT04sY0FBY00sS0FBSztnQkFDMUJDLFFBQVFQLGNBQWNPLE1BQU07WUFDOUI7WUFFQSxPQUFPO1lBQ1AsTUFBTUMsY0FBYztnQkFDbEIsR0FBR2YsMERBQXNCO2dCQUN6QixHQUFHLElBQUksQ0FBQ1EsTUFBTSxDQUFDQSxNQUFNO2dCQUNyQlEsSUFBSSxJQUFJLENBQUNSLE1BQU0sQ0FBQ0MsU0FBUztnQkFDekJRLE1BQU0sSUFBSSxDQUFDVCxNQUFNLENBQUNTLElBQUk7Z0JBQ3RCQyxVQUFVLElBQUksQ0FBQ1YsTUFBTSxDQUFDVSxRQUFRO2dCQUM5QixXQUFXO2dCQUNYTCxPQUFPTixjQUFjTSxLQUFLLElBQUk7Z0JBQzlCQyxRQUFRUCxjQUFjTyxNQUFNLElBQUk7WUFDbEM7WUFFQSxPQUFPO1lBQ1AsSUFBSSxDQUFDVCxlQUFlLEdBQUcsSUFBSUQsY0FBY1c7WUFDekMsSUFBSSxDQUFDSSxhQUFhLEdBQUc7WUFFckJSLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU8sSUFBSSxDQUFDUCxlQUFlO1FBRTdCLEVBQUUsT0FBT2UsT0FBTztZQUNkVCxRQUFRUyxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNLElBQUlDLE1BQU0sdUNBQWdHLE9BQXpERCxpQkFBaUJDLFFBQVFELE1BQU1FLE9BQU8sR0FBRztRQUNsRztJQUNGO0lBRUE7O0dBRUMsR0FDREMsY0FBNEM7UUFDMUMsT0FBTyxJQUFJLENBQUNsQixlQUFlO0lBQzdCO0lBRUE7O0dBRUMsR0FDRG1CLFVBQW1CO1FBQ2pCLE9BQU8sSUFBSSxDQUFDTCxhQUFhLElBQUksSUFBSSxDQUFDZCxlQUFlLEtBQUs7SUFDeEQ7SUFFQTs7R0FFQyxHQUNEb0IsUUFBUVIsSUFBaUIsRUFBUTtRQUMvQixJQUFJLENBQUMsSUFBSSxDQUFDWixlQUFlLEVBQUU7WUFDekIsTUFBTSxJQUFJZ0IsTUFBTTtRQUNsQjtRQUVBLElBQUk7WUFDRixJQUFJLENBQUNoQixlQUFlLENBQUNvQixPQUFPLENBQUNSO1lBQzdCTixRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9RLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0RNLFdBQVdULElBQWlCLEVBQVE7UUFDbEMsSUFBSSxDQUFDLElBQUksQ0FBQ1osZUFBZSxFQUFFO1lBQ3pCLE1BQU0sSUFBSWdCLE1BQU07UUFDbEI7UUFFQSxJQUFJO1lBQ0YseUJBQXlCO1lBQ3pCLElBQUksT0FBTyxJQUFJLENBQUNoQixlQUFlLENBQUNxQixVQUFVLEtBQUssWUFBWTtnQkFDekQsSUFBSSxDQUFDckIsZUFBZSxDQUFDcUIsVUFBVSxDQUFDVDtZQUNsQyxPQUFPO2dCQUNMLE9BQU87Z0JBQ1AsSUFBSSxDQUFDWixlQUFlLENBQUNvQixPQUFPLENBQUNSO1lBQy9CO1lBQ0FOLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT1EsT0FBTztZQUNkVCxRQUFRUyxLQUFLLENBQUMsYUFBYUE7WUFDM0IsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRE8sVUFBaUM7WUFBekJDLGFBQUFBLGlFQUFhO1FBQ25CLElBQUksQ0FBQyxJQUFJLENBQUN2QixlQUFlLEVBQUU7WUFDekIsTUFBTSxJQUFJZ0IsTUFBTTtRQUNsQjtRQUVBLElBQUk7WUFDRixPQUFPLElBQUksQ0FBQ2hCLGVBQWUsQ0FBQ3NCLE9BQU8sQ0FBQ0M7UUFDdEMsRUFBRSxPQUFPUixPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyxhQUFhQTtZQUMzQixNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEUyxVQUFnQjtRQUNkLElBQUksQ0FBQyxJQUFJLENBQUN4QixlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLGNBQWM7WUFDZCxJQUFJLENBQUNBLGVBQWUsQ0FBQ3lCLElBQUksQ0FBQ0MsR0FBRztZQUM3QnBCLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT1EsT0FBTztZQUNkVCxRQUFRUyxLQUFLLENBQUMsYUFBYUE7UUFDN0I7SUFDRjtJQUVBOztHQUVDLEdBQ0RZLFlBQWtCO1FBQ2hCLElBQUksQ0FBQyxJQUFJLENBQUMzQixlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLElBQUksQ0FBQ0EsZUFBZSxDQUFDeUIsSUFBSSxDQUFDRyxLQUFLO1lBQy9CdEIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPUSxPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyxhQUFhQTtRQUM3QjtJQUNGO0lBRUE7O0dBRUMsR0FDRGMsaUJBQWlCQyxPQUFlLEVBQUVDLE9BQWUsRUFBNEI7UUFDM0UsSUFBSSxDQUFDLElBQUksQ0FBQy9CLGVBQWUsRUFBRTtZQUN6QixPQUFPO2dCQUFFZ0MsR0FBR0Y7Z0JBQVNHLEdBQUdGO1lBQVE7UUFDbEM7UUFFQSxJQUFJO1lBQ0YsT0FBTyxJQUFJLENBQUMvQixlQUFlLENBQUNrQyxLQUFLLENBQUNKLFNBQVNDO1FBQzdDLEVBQUUsT0FBT2hCLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE9BQU87Z0JBQUVpQixHQUFHRjtnQkFBU0csR0FBR0Y7WUFBUTtRQUNsQztJQUNGO0lBRUE7O0dBRUMsR0FDRDlCLFVBQWdCO1FBQ2QsSUFBSSxJQUFJLENBQUNELGVBQWUsRUFBRTtZQUN4QixJQUFJO2dCQUNGLElBQUksQ0FBQ0EsZUFBZSxDQUFDQyxPQUFPO2dCQUM1QkssUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPUSxPQUFPO2dCQUNkVCxRQUFRUyxLQUFLLENBQUMsMEJBQTBCQTtZQUMxQztRQUNGO1FBRUEsSUFBSSxDQUFDZixlQUFlLEdBQUc7UUFDdkIsSUFBSSxDQUFDYyxhQUFhLEdBQUc7SUFDdkI7SUFuTEFxQixZQUFZaEMsTUFBeUIsQ0FBRTthQUovQkgsa0JBQWdEO2FBRWhEYyxnQkFBZ0I7UUFHdEIsSUFBSSxDQUFDWCxNQUFNLEdBQUdBO0lBQ2hCO0FBa0xGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9Db3JlTWFuYWdlci50cz8yNDU1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiBDb3JlTWFuYWdlciAtIOaguOW/g+euoeeQhuWZqFxyXG4gKiDotJ/otKNTaW1wbGVNaW5kTWFw5a6e5L6L55qE5Yib5bu644CB5Yid5aeL5YyW5ZKM5pWw5o2u566h55CGXHJcbiAqIFxyXG4gKiDogYzotKPvvJpcclxuICogLSBTaW1wbGVNaW5kTWFw5a6e5L6L55Sf5ZG95ZGo5pyf566h55CGXHJcbiAqIC0g5pWw5o2u6K6+572u5ZKM5pu05pawXHJcbiAqIC0g5Z+656GA6YWN572u566h55CGXHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgU2ltcGxlTWluZE1hcEluc3RhbmNlLCBNaW5kTWFwTm9kZSwgTWluZE1hcENvbmZpZywgREVGQVVMVF9NSU5ETUFQX0NPTkZJRyB9IGZyb20gJy4uL3R5cGVzJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ29yZU1hbmFnZXJDb25maWcge1xyXG4gIGNvbnRhaW5lcjogSFRNTEVsZW1lbnQ7XHJcbiAgZGF0YTogTWluZE1hcE5vZGU7XHJcbiAgcmVhZG9ubHk6IGJvb2xlYW47XHJcbiAgY29uZmlnOiBQYXJ0aWFsPE1pbmRNYXBDb25maWc+O1xyXG59XHJcblxyXG5leHBvcnQgY2xhc3MgQ29yZU1hbmFnZXIge1xyXG4gIHByaXZhdGUgbWluZE1hcEluc3RhbmNlOiBTaW1wbGVNaW5kTWFwSW5zdGFuY2UgfCBudWxsID0gbnVsbDtcclxuICBwcml2YXRlIGNvbmZpZzogQ29yZU1hbmFnZXJDb25maWc7XHJcbiAgcHJpdmF0ZSBpc0luaXRpYWxpemVkID0gZmFsc2U7XHJcblxyXG4gIGNvbnN0cnVjdG9yKGNvbmZpZzogQ29yZU1hbmFnZXJDb25maWcpIHtcclxuICAgIHRoaXMuY29uZmlnID0gY29uZmlnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5Yid5aeL5YyWU2ltcGxlTWluZE1hcOWunuS+i1xyXG4gICAqL1xyXG4gIGFzeW5jIGluaXRpYWxpemUoKTogUHJvbWlzZTxTaW1wbGVNaW5kTWFwSW5zdGFuY2U+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOWKqOaAgeWvvOWFpVNpbXBsZU1pbmRNYXDpgb/lhY1TU1Lpl67pophcclxuICAgICAgY29uc3QgeyBkZWZhdWx0OiBTaW1wbGVNaW5kTWFwIH0gPSBhd2FpdCBpbXBvcnQoJ3NpbXBsZS1taW5kLW1hcCcpO1xyXG5cclxuICAgICAgLy8g5riF55CG546w5pyJ5a6e5L6LXHJcbiAgICAgIGlmICh0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLmRlc3Ryb3koKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8g56Gu5L+d5a655Zmo5pyJ5piO56Gu55qE5bC65a+4XHJcbiAgICAgIGNvbnN0IGNvbnRhaW5lclJlY3QgPSB0aGlzLmNvbmZpZy5jb250YWluZXIuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OQIOWuueWZqOWwuuWvuDonLCB7IFxyXG4gICAgICAgIHdpZHRoOiBjb250YWluZXJSZWN0LndpZHRoLCBcclxuICAgICAgICBoZWlnaHQ6IGNvbnRhaW5lclJlY3QuaGVpZ2h0IFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIOWQiOW5tumFjee9rlxyXG4gICAgICBjb25zdCBmaW5hbENvbmZpZyA9IHtcclxuICAgICAgICAuLi5ERUZBVUxUX01JTkRNQVBfQ09ORklHLFxyXG4gICAgICAgIC4uLnRoaXMuY29uZmlnLmNvbmZpZyxcclxuICAgICAgICBlbDogdGhpcy5jb25maWcuY29udGFpbmVyLFxyXG4gICAgICAgIGRhdGE6IHRoaXMuY29uZmlnLmRhdGEsXHJcbiAgICAgICAgcmVhZG9ubHk6IHRoaXMuY29uZmlnLnJlYWRvbmx5LFxyXG4gICAgICAgIC8vIOaYjuehruiuvue9rueUu+W4g+WwuuWvuFxyXG4gICAgICAgIHdpZHRoOiBjb250YWluZXJSZWN0LndpZHRoIHx8IDgwMCxcclxuICAgICAgICBoZWlnaHQ6IGNvbnRhaW5lclJlY3QuaGVpZ2h0IHx8IDYwMFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8g5Yib5bu65a6e5L6LXHJcbiAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlID0gbmV3IFNpbXBsZU1pbmRNYXAoZmluYWxDb25maWcpIGFzIFNpbXBsZU1pbmRNYXBJbnN0YW5jZTtcclxuICAgICAgdGhpcy5pc0luaXRpYWxpemVkID0gdHJ1ZTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgU2ltcGxlTWluZE1hcOWunuS+i+WIneWni+WMluaIkOWKnycpO1xyXG4gICAgICByZXR1cm4gdGhpcy5taW5kTWFwSW5zdGFuY2U7XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIFNpbXBsZU1pbmRNYXDliJ3lp4vljJblpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBpbml0aWFsaXplIFNpbXBsZU1pbmRNYXA6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcid9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDojrflj5ZTaW1wbGVNaW5kTWFw5a6e5L6LXHJcbiAgICovXHJcbiAgZ2V0SW5zdGFuY2UoKTogU2ltcGxlTWluZE1hcEluc3RhbmNlIHwgbnVsbCB7XHJcbiAgICByZXR1cm4gdGhpcy5taW5kTWFwSW5zdGFuY2U7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDmo4Dmn6XmmK/lkKblt7LliJ3lp4vljJZcclxuICAgKi9cclxuICBpc1JlYWR5KCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuaXNJbml0aWFsaXplZCAmJiB0aGlzLm1pbmRNYXBJbnN0YW5jZSAhPT0gbnVsbDtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOiuvue9ruaVsOaNrlxyXG4gICAqL1xyXG4gIHNldERhdGEoZGF0YTogTWluZE1hcE5vZGUpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwSW5zdGFuY2UpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdNaW5kTWFwIGluc3RhbmNlIG5vdCBpbml0aWFsaXplZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLnNldERhdGEoZGF0YSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfinIUg5pWw5o2u6K6+572u5oiQ5YqfJyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5pWw5o2u6K6+572u5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDmm7TmlrDmlbDmja7vvIjmgKfog73kvJjljJbniYjmnKzvvIlcclxuICAgKi9cclxuICB1cGRhdGVEYXRhKGRhdGE6IE1pbmRNYXBOb2RlKTogdm9pZCB7XHJcbiAgICBpZiAoIXRoaXMubWluZE1hcEluc3RhbmNlKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignTWluZE1hcCBpbnN0YW5jZSBub3QgaW5pdGlhbGl6ZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDkvb/nlKjlrpjmlrnnmoR1cGRhdGVEYXRh5pa55rOV77yM5oCn6IO95pu05aW9XHJcbiAgICAgIGlmICh0eXBlb2YgdGhpcy5taW5kTWFwSW5zdGFuY2UudXBkYXRlRGF0YSA9PT0gJ2Z1bmN0aW9uJykge1xyXG4gICAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLnVwZGF0ZURhdGEoZGF0YSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8g6ZmN57qn5pa55qGIXHJcbiAgICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2Uuc2V0RGF0YShkYXRhKTtcclxuICAgICAgfVxyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIOaVsOaNruabtOaWsOaIkOWKnycpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIOaVsOaNruabtOaWsOWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6I635Y+W5pWw5o2uXHJcbiAgICovXHJcbiAgZ2V0RGF0YSh3aXRoQ29uZmlnID0gZmFsc2UpOiBhbnkge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ01pbmRNYXAgaW5zdGFuY2Ugbm90IGluaXRpYWxpemVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgcmV0dXJuIHRoaXMubWluZE1hcEluc3RhbmNlLmdldERhdGEod2l0aENvbmZpZyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W5pWw5o2u5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDpgILlupTnlLvluIPlpKflsI9cclxuICAgKi9cclxuICBmaXRWaWV3KCk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOS9v+eUqOWumOaWuUFQSemAguW6lOeUu+W4g1xyXG4gICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS52aWV3LmZpdCgpO1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIOeUu+W4g+mAguW6lOWujOaIkCcpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIOeUu+W4g+mAguW6lOWksei0pTonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDph43nva7op4blm75cclxuICAgKi9cclxuICByZXNldFZpZXcoKTogdm9pZCB7XHJcbiAgICBpZiAoIXRoaXMubWluZE1hcEluc3RhbmNlKSByZXR1cm47XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2Uudmlldy5yZXNldCgpO1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIOinhuWbvumHjee9ruWujOaIkCcpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIOinhuWbvumHjee9ruWksei0pTonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDlnZDmoIfovazmjaJcclxuICAgKi9cclxuICB0b0NhbnZhc1Bvc2l0aW9uKHNjcmVlblg6IG51bWJlciwgc2NyZWVuWTogbnVtYmVyKTogeyB4OiBudW1iZXI7IHk6IG51bWJlciB9IHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwSW5zdGFuY2UpIHtcclxuICAgICAgcmV0dXJuIHsgeDogc2NyZWVuWCwgeTogc2NyZWVuWSB9O1xyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHJldHVybiB0aGlzLm1pbmRNYXBJbnN0YW5jZS50b1BvcyhzY3JlZW5YLCBzY3JlZW5ZKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlnZDmoIfovazmjaLlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4geyB4OiBzY3JlZW5YLCB5OiBzY3JlZW5ZIH07XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDplIDmr4Hlrp7kvotcclxuICAgKi9cclxuICBkZXN0cm95KCk6IHZvaWQge1xyXG4gICAgaWYgKHRoaXMubWluZE1hcEluc3RhbmNlKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2UuZGVzdHJveSgpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgU2ltcGxlTWluZE1hcOWunuS+i+mUgOavgeWujOaIkCcpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDplIDmr4FTaW1wbGVNaW5kTWFw5a6e5L6L5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHRoaXMubWluZE1hcEluc3RhbmNlID0gbnVsbDtcclxuICAgIHRoaXMuaXNJbml0aWFsaXplZCA9IGZhbHNlO1xyXG4gIH1cclxufSJdLCJuYW1lcyI6WyJERUZBVUxUX01JTkRNQVBfQ09ORklHIiwiQ29yZU1hbmFnZXIiLCJpbml0aWFsaXplIiwiZGVmYXVsdCIsIlNpbXBsZU1pbmRNYXAiLCJtaW5kTWFwSW5zdGFuY2UiLCJkZXN0cm95IiwiY29udGFpbmVyUmVjdCIsImNvbmZpZyIsImNvbnRhaW5lciIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsImNvbnNvbGUiLCJsb2ciLCJ3aWR0aCIsImhlaWdodCIsImZpbmFsQ29uZmlnIiwiZWwiLCJkYXRhIiwicmVhZG9ubHkiLCJpc0luaXRpYWxpemVkIiwiZXJyb3IiLCJFcnJvciIsIm1lc3NhZ2UiLCJnZXRJbnN0YW5jZSIsImlzUmVhZHkiLCJzZXREYXRhIiwidXBkYXRlRGF0YSIsImdldERhdGEiLCJ3aXRoQ29uZmlnIiwiZml0VmlldyIsInZpZXciLCJmaXQiLCJyZXNldFZpZXciLCJyZXNldCIsInRvQ2FudmFzUG9zaXRpb24iLCJzY3JlZW5YIiwic2NyZWVuWSIsIngiLCJ5IiwidG9Qb3MiLCJjb25zdHJ1Y3RvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ })

});