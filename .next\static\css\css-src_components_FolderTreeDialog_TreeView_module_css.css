/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./src/components/FolderTreeDialog/TreeView.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
.TreeView_treeView__xNxgv {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.TreeView_treeNode__O5DIB {
  display: flex;
  flex-direction: column;
}

.TreeView_treeNodeContent__GmwuF {
  display: flex;
  cursor: pointer;
  align-items: center;
  gap: 0.5rem;
  border-radius: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.TreeView_treeNodeContent__GmwuF:hover {
  background-color: rgb(245 158 11 / 0.1);
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.TreeView_treeNodeContent__GmwuF {
  border-width: 1px;
  border-color: transparent;
}

.TreeView_treeNodeContent__GmwuF:hover {
  border-color: rgb(245 158 11 / 0.2);
}

.TreeView_treeNodeContent__GmwuF {
  min-height: 36px;
  position: relative;
}

.TreeView_expandButton__AzRy_ {
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  border-width: 1px;
  border-color: rgb(245 158 11 / 0.3);
  background-color: rgb(245 158 11 / 0.1);
}

.TreeView_expandButton__AzRy_:hover {
  border-color: rgb(245 158 11 / 0.5);
  background-color: rgb(245 158 11 / 0.2);
}

.TreeView_expandButton__AzRy_ {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.TreeView_expandButton__AzRy_:hover {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

.TreeView_expandButton__AzRy_.TreeView_hasChildren__dP1Na {
  cursor: pointer;
}

.TreeView_expandButton__AzRy_.TreeView_hasChildren__dP1Na:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.TreeView_expandButton__AzRy_.TreeView_noChildren__T86Nj {
  cursor: default;
  border-color: transparent;
  background-color: transparent;
  opacity: 0.3;
}

.TreeView_nodeIcon__3aT2B {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.TreeView_nodeName__aUSM1 {
  flex: 1 1 0%;
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.TreeView_childNodes__qrEgJ {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.TreeView_treeCheckbox__blKaw {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border-width: 2px;
  border-color: rgb(245 158 11 / 0.5);
  background-color: rgb(31 41 55 / 0.5);
}

.TreeView_treeCheckbox__blKaw:checked {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.TreeView_treeCheckbox__blKaw:hover {
  border-color: rgb(245 158 11 / 0.7);
  background-color: rgb(245 158 11 / 0.1);
}

.TreeView_treeCheckbox__blKaw:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(245 158 11 / 0.5);
  --tw-ring-offset-width: 0px;
}

.TreeView_treeCheckbox__blKaw {
  cursor: pointer;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  position: relative;
}

.TreeView_treeCheckbox__blKaw:checked::after {
  content: '✓';
  position: absolute;
  inset: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* 搜索结果样式 */
.TreeView_searchResult__j_l_v {
  border-color: rgb(245 158 11 / 0.4);
  background-color: rgb(245 158 11 / 0.2);
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(245 158 11 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
  animation: TreeView_pulse__x1kLS 2s infinite;
  position: relative;
}

.TreeView_searchResult__j_l_v::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  bottom: 0px;
  width: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
  animation: TreeView_glow__dsuzL 2s infinite;
}

.TreeView_searchResultText__SZhiG {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}

.TreeView_searchResultHighlight__tFQpT {
  border-radius: 0.25rem;
  background-color: rgb(245 158 11 / 0.4);
  padding-left: 0.125rem;
  padding-right: 0.125rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.TreeView_searchResultsSection___Za_K {
  margin-bottom: 1rem;
  border-bottom-width: 1px;
  border-color: rgb(245 158 11 / 0.3);
  padding-bottom: 0.5rem;
}

.TreeView_searchResultsTitle__92BbO {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

@keyframes TreeView_pulse__x1kLS {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

@keyframes TreeView_glow__dsuzL {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}
