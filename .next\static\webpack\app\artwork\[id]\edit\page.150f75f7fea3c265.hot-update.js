"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = (param)=>{\n    let { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, className = \"\", config = {} } = param;\n    _s();\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            console.warn(\"⚠️ Container element not ready, retrying...\");\n            // 短暂延迟后重试\n            setTimeout(()=>{\n                if (containerRef.current) {\n                    initializeMindMap();\n                } else {\n                    setRenderError(\"Container element not found after retry\");\n                }\n            }, 200);\n            return;\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            console.log(\"\\uD83D\\uDE80 开始初始化思维导图:\", {\n                container: containerRef.current,\n                dataPreview: mindMapData\n            });\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange,\n                onRenderComplete\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n            console.log(\"✅ 思维导图初始化成功\");\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _selectionManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap();\n            }, 100);\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeMindMap,\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width,\n            height,\n            background: \"#1a1a1a\",\n            border: \"1px solid var(--color-primary-green, #8FBC8F)\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 2px 8px rgba(143, 188, 143, 0.3)\"\n        },\n        children: !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: \"absolute\",\n                top: \"10px\",\n                right: \"10px\",\n                background: \"rgba(143, 188, 143, 0.9)\",\n                color: \"#fff\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontSize: \"12px\",\n                zIndex: 100,\n                pointerEvents: \"none\",\n                fontFamily: \"var(--font-family-handwritten)\"\n            },\n            children: \"\\uD83D\\uDCA1 右键长按可框选多个节点\"\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 306,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MindMapRenderer, \"WsC+8anm46mIX4UTf2kk6BEDs5Y=\");\n_c = MindMapRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c;\n$RefreshReg$(_c, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});