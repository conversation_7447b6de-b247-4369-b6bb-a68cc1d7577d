"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = (param)=>{\n    let { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, className = \"\", config = {} } = param;\n    _s();\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _selectionManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            console.log(\"\\uD83D\\uDE80 开始初始化思维导图:\", {\n                container: containerRef.current,\n                dataPreview: mindMapData\n            });\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange,\n                onRenderComplete\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n            console.log(\"✅ 思维导图初始化成功\");\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeMindMap,\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: \"absolute\",\n                top: \"10px\",\n                right: \"10px\",\n                background: \"rgba(255, 215, 0, 0.9)\",\n                color: \"#000\",\n                padding: \"6px 12px\",\n                borderRadius: \"6px\",\n                fontSize: \"12px\",\n                zIndex: 100,\n                pointerEvents: \"none\",\n                fontFamily: \"var(--font-family-handwritten)\",\n                fontWeight: \"600\",\n                border: \"1px solid #FFA500\"\n            },\n            children: \"\\uD83D\\uDCA1 右键长按可框选多个节点\"\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 307,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MindMapRenderer, \"e2zxza7XWXt5cb2mhR/zP1o8j/g=\");\n_c = MindMapRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c;\n$RefreshReg$(_c, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});