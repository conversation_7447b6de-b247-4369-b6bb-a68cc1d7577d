"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleManager: function() { return /* binding */ StyleManager; }\n/* harmony export */ });\n/**\r\n * StyleManager - 样式管理器\r\n * 负责SimpleMindMap主题和样式的统一管理\r\n * \r\n * 职责：\r\n * - 主题切换和配置\r\n * - 自定义样式应用\r\n * - 样式状态管理\r\n * \r\n * 设计原则：\r\n * - 官方API优先：使用SimpleMindMap官方主题API\r\n * - 简洁高效：最小化样式配置，避免过度自定义\r\n */ class StyleManager {\n    /**\r\n   * 初始化样式管理器\r\n   */ async initialize() {\n        try {\n            // 应用初始主题\n            await this.applyTheme(this.currentTheme);\n            this.isInitialized = true;\n            console.log(\"✅ 样式管理器初始化完成，主题:\", this.currentTheme);\n        } catch (error) {\n            console.error(\"❌ 样式管理器初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ async applyTheme(theme) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not available\");\n        }\n        try {\n            // 直接使用我们的自定义主题配置，不依赖SimpleMindMap内置主题\n            const themeConfig = this.getThemeConfig(theme);\n            this.mindMapInstance.setThemeConfig(themeConfig, false);\n            this.currentTheme = theme;\n            console.log(\"✅ 自定义主题应用成功: \".concat(theme), themeConfig);\n        } catch (error) {\n            console.error(\"❌ 主题应用失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式 - 金色高亮\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#FFD700\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 4,\n                fontSize: 13,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#000000\",\n                padding: [\n                    6,\n                    10\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 0.95\n            },\n            // 展开按钮样式 - 金色\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#FFD700\",\n                fillColor: \"#0a0a0a\",\n                strokeColor: \"#FFD700\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式 - 金色系\n            associativeLine: {\n                strokeColor: \"#DAA520\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式 - 亮金色\n            activeNodeStyle: {\n                strokeColor: \"#FFFF00\",\n                strokeWidth: 4,\n                fillColor: \"rgba(255, 215, 0, 0.15)\"\n            },\n            // 悬停状态样式 - 金色光晕\n            hoverNodeStyle: {\n                fillColor: \"rgba(255, 215, 0, 0.1)\",\n                strokeColor: \"#FFA500\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 更新主题\r\n   */ async updateTheme(theme) {\n        if (theme === this.currentTheme) return;\n        await this.applyTheme(theme);\n    }\n    /**\r\n   * 获取当前主题\r\n   */ getCurrentTheme() {\n        return this.currentTheme;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 销毁样式管理器\r\n   */ destroy() {\n        this.isInitialized = false;\n        console.log(\"✅ 样式管理器销毁完成\");\n    }\n    constructor(mindMapInstance, theme = \"dark\"){\n        this.isInitialized = false;\n        this.mindMapInstance = mindMapInstance;\n        this.currentTheme = theme;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ })

});