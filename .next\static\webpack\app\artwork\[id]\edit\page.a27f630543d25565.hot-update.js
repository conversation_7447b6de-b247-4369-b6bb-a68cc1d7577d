"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n * \r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \nclass CoreManager {\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        try {\n            // 动态导入SimpleMindMap避免SSR问题\n            const { default: SimpleMindMap } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_simple-mind-map_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map */ \"(app-pages-browser)/./node_modules/simple-mind-map/index.js\"));\n            // 清理现有实例\n            if (this.mindMapInstance) {\n                this.mindMapInstance.destroy();\n            }\n            // 确保容器有明确的尺寸\n            const containerRect = this.config.container.getBoundingClientRect();\n            console.log(\"\\uD83D\\uDCD0 容器尺寸:\", {\n                width: containerRect.width,\n                height: containerRect.height\n            });\n            // 合并配置\n            const finalConfig = {\n                ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                ...this.config.config,\n                el: this.config.container,\n                data: this.config.data,\n                readonly: this.config.readonly,\n                // 明确设置画布尺寸\n                width: containerRect.width || 800,\n                height: containerRect.height || 600\n            };\n            // 创建实例\n            this.mindMapInstance = new SimpleMindMap(finalConfig);\n            this.isInitialized = true;\n            console.log(\"✅ SimpleMindMap实例初始化成功\");\n            return this.mindMapInstance;\n        } catch (error) {\n            console.error(\"❌ SimpleMindMap初始化失败:\", error);\n            throw new Error(\"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\"));\n        }\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            this.mindMapInstance.setData(data);\n            console.log(\"✅ 数据设置成功\");\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n            console.log(\"✅ 数据更新成功\");\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 容器尺寸变化后调整画布\r\n   */ resize() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API调整容器尺寸变化\n            this.mindMapInstance.resize();\n            console.log(\"✅ 画布尺寸调整完成\");\n        } catch (error) {\n            console.error(\"❌ 画布尺寸调整失败:\", error);\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n            console.log(\"✅ 画布适应完成\");\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n            console.log(\"✅ 视图重置完成\");\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 销毁实例\r\n   */ destroy() {\n        if (this.mindMapInstance) {\n            try {\n                this.mindMapInstance.destroy();\n                console.log(\"✅ SimpleMindMap实例销毁完成\");\n            } catch (error) {\n                console.error(\"❌ 销毁SimpleMindMap实例失败:\", error);\n            }\n        }\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ })

});