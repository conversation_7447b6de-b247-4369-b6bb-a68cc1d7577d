"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleManager: function() { return /* binding */ StyleManager; }\n/* harmony export */ });\n/**\r\n * StyleManager - 样式管理器\r\n * 负责SimpleMindMap主题和样式的统一管理\r\n * \r\n * 职责：\r\n * - 主题切换和配置\r\n * - 自定义样式应用\r\n * - 样式状态管理\r\n * \r\n * 设计原则：\r\n * - 官方API优先：使用SimpleMindMap官方主题API\r\n * - 简洁高效：最小化样式配置，避免过度自定义\r\n */ class StyleManager {\n    /**\r\n   * 初始化样式管理器\r\n   */ async initialize() {\n        try {\n            // 应用初始主题\n            await this.applyTheme(this.currentTheme);\n            this.isInitialized = true;\n            console.log(\"✅ 样式管理器初始化完成，主题:\", this.currentTheme);\n        } catch (error) {\n            console.error(\"❌ 样式管理器初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ async applyTheme(theme) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not available\");\n        }\n        try {\n            // 使用官方API设置主题\n            if (typeof this.mindMapInstance.setTheme === \"function\") {\n                this.mindMapInstance.setTheme(theme);\n            } else if (typeof this.mindMapInstance.setThemeConfig === \"function\") {\n                // 降级方案：使用主题配置API\n                const themeConfig = this.getThemeConfig(theme);\n                this.mindMapInstance.setThemeConfig(themeConfig, false);\n            }\n            this.currentTheme = theme;\n            console.log(\"✅ 主题应用成功: \".concat(theme));\n        } catch (error) {\n            console.error(\"❌ 主题应用失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式 - 金色高亮\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#FFD700\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 4,\n                fontSize: 13,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#000000\",\n                padding: [\n                    6,\n                    10\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 0.95\n            },\n            // 展开按钮样式 - 金色\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#FFD700\",\n                fillColor: \"#0a0a0a\",\n                strokeColor: \"#FFD700\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式 - 金色系\n            associativeLine: {\n                strokeColor: \"#DAA520\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式 - 亮金色\n            activeNodeStyle: {\n                strokeColor: \"#FFFF00\",\n                strokeWidth: 4,\n                fillColor: \"rgba(255, 215, 0, 0.15)\"\n            },\n            // 悬停状态样式 - 金色光晕\n            hoverNodeStyle: {\n                fillColor: \"rgba(255, 215, 0, 0.1)\",\n                strokeColor: \"#FFA500\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 更新主题\r\n   */ async updateTheme(theme) {\n        if (theme === this.currentTheme) return;\n        await this.applyTheme(theme);\n    }\n    /**\r\n   * 获取当前主题\r\n   */ getCurrentTheme() {\n        return this.currentTheme;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 销毁样式管理器\r\n   */ destroy() {\n        this.isInitialized = false;\n        console.log(\"✅ 样式管理器销毁完成\");\n    }\n    constructor(mindMapInstance, theme = \"dark\"){\n        this.isInitialized = false;\n        this.mindMapInstance = mindMapInstance;\n        this.currentTheme = theme;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ })

});