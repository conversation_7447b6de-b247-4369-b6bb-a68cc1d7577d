/**
 * StyleManager - 样式管理器
 * 负责SimpleMindMap主题和样式的统一管理
 * 
 * 职责：
 * - 主题切换和配置
 * - 自定义样式应用
 * - 样式状态管理
 * 
 * 设计原则：
 * - 官方API优先：使用SimpleMindMap官方主题API
 * - 简洁高效：最小化样式配置，避免过度自定义
 */

import { SimpleMindMapInstance, MindMapTheme } from '../types';

export class StyleManager {
  private mindMapInstance: SimpleMindMapInstance;
  private currentTheme: MindMapTheme;
  private isInitialized = false;

  constructor(mindMapInstance: SimpleMindMapInstance, theme: MindMapTheme = 'dark') {
    this.mindMapInstance = mindMapInstance;
    this.currentTheme = theme;
  }

  /**
   * 初始化样式管理器
   */
  async initialize(): Promise<void> {
    try {
      // 应用初始主题
      await this.applyTheme(this.currentTheme);
      
      this.isInitialized = true;
      console.log('✅ 样式管理器初始化完成，主题:', this.currentTheme);
    } catch (error) {
      console.error('❌ 样式管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 应用主题
   */
  async applyTheme(theme: MindMapTheme): Promise<void> {
    if (!this.mindMapInstance) {
      throw new Error('MindMap instance not available');
    }

    try {
      // 直接使用我们的自定义主题配置，不依赖SimpleMindMap内置主题
      const themeConfig = this.getThemeConfig(theme);
      this.mindMapInstance.setThemeConfig(themeConfig, false);
      
      this.currentTheme = theme;
      console.log(`✅ 自定义主题应用成功: ${theme}`, themeConfig);
    } catch (error) {
      console.error('❌ 主题应用失败:', error);
      throw error;
    }
  }

  /**
   * 获取主题配置
   */
  getThemeConfig(theme: MindMapTheme): any {
    switch (theme) {
      case 'dark':
        return this.getDarkThemeConfig();
      case 'light':
        return this.getLightThemeConfig();
      default:
        return this.getDarkThemeConfig();
    }
  }

  /**
   * 获取深色主题配置 - 黑金配色
   */
  public getDarkThemeConfig(): any {
    return {
      // 背景配置 - 深黑色
      backgroundColor: '#0a0a0a',
      backgroundImage: '',
      
      // 连线配置 - 金色
      lineColor: '#FFD700',
      lineWidth: 2,
      lineStyle: 'solid',
      lineDasharray: 'none',
      lineOpacity: 0.9,
      
      // 根节点样式 - 黑金主题
      root: {
        shape: 'rectangle',
        fillColor: '#1a1a1a',
        borderColor: '#FFD700',
        borderWidth: 3,
        borderRadius: 8,
        fontSize: 20,
        fontFamily: 'var(--font-family-handwritten, "Microsoft YaHei", "PingFang SC", sans-serif)',
        fontWeight: 700,
        color: '#FFD700',
        padding: [15, 20],
        margin: [0, 0, 0, 0],
        opacity: 1,
        textDecoration: 'none',
        fontStyle: 'normal'
      },
      
      // 二级节点样式 - 渐变金色
      second: {
        shape: 'rectangle',
        fillColor: '#2a2a2a',
        borderColor: '#FFA500',
        borderWidth: 2,
        borderRadius: 6,
        fontSize: 17,
        fontFamily: 'var(--font-family-handwritten, "Microsoft YaHei", "PingFang SC", sans-serif)',
        fontWeight: 600,
        color: '#FFA500',
        padding: [10, 15],
        margin: [5, 5, 5, 5],
        opacity: 1,
        textDecoration: 'none',
        fontStyle: 'normal'
      },
      
      // 三级及以下节点样式 - 浅金色
      node: {
        shape: 'rectangle',
        fillColor: '#1e1e1e',
        borderColor: '#DAA520',
        borderWidth: 1,
        borderRadius: 4,
        fontSize: 15,
        fontFamily: 'var(--font-family-handwritten, "Microsoft YaHei", "PingFang SC", sans-serif)',
        fontWeight: 500,
        color: '#DAA520',
        padding: [8, 12],
        margin: [3, 3, 3, 3],
        opacity: 1,
        textDecoration: 'none',
        fontStyle: 'normal'
      },
      
      // 概要节点样式 - 金色高亮
      generalization: {
        shape: 'rectangle',
        fillColor: '#FFD700',
        borderColor: '#FFA500',
        borderWidth: 2,
        borderRadius: 4,
        fontSize: 13,
        fontFamily: 'var(--font-family-handwritten, "Microsoft YaHei", "PingFang SC", sans-serif)',
        fontWeight: 600,
        color: '#000000',
        padding: [6, 10],
        margin: [3, 3, 3, 3],
        opacity: 0.95
      },
      
      // 展开按钮样式 - 金色
      expandBtnSize: 16,
      expandBtnStyle: {
        color: '#FFD700',
        fillColor: '#0a0a0a',
        strokeColor: '#FFD700',
        strokeWidth: 2,
        radius: 8
      },
      
      // 连接点样式 - 金色系
      associativeLine: {
        strokeColor: '#DAA520',
        strokeWidth: 2,
        strokeDasharray: '5,5'
      },
      
      // 激活状态样式 - 亮金色
      activeNodeStyle: {
        strokeColor: '#FFFF00',
        strokeWidth: 4,
        fillColor: 'rgba(255, 215, 0, 0.15)'
      },
      
      // 悬停状态样式 - 金色光晕
      hoverNodeStyle: {
        fillColor: 'rgba(255, 215, 0, 0.1)',
        strokeColor: '#FFA500',
        strokeWidth: 2
      }
    };
  }

  /**
   * 获取浅色主题配置
   */
  private getLightThemeConfig(): any {
    return {
      backgroundColor: '#ffffff',
      backgroundImage: '',
      lineColor: '#666666',
      lineWidth: 1,
      
      root: {
        shape: 'rectangle',
        fillColor: '#f3f4f6',
        borderColor: '#374151',
        borderWidth: 2,
        borderRadius: 8,
        fontSize: 18,
        fontWeight: 600,
        color: '#111827',
        padding: [12, 16]
      },
      
      second: {
        shape: 'rectangle',
        fillColor: '#f9fafb',
        borderColor: '#6b7280',
        borderWidth: 1,
        borderRadius: 6,
        fontSize: 16,
        fontWeight: 500,
        color: '#374151',
        padding: [8, 12]
      },
      
      node: {
        shape: 'rectangle',
        fillColor: '#ffffff',
        borderColor: '#d1d5db',
        borderWidth: 1,
        borderRadius: 4,
        fontSize: 14,
        fontWeight: 400,
        color: '#4b5563',
        padding: [6, 10]
      }
    };
  }

  /**
   * 更新主题
   */
  async updateTheme(theme: MindMapTheme): Promise<void> {
    if (theme === this.currentTheme) return;
    
    await this.applyTheme(theme);
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme(): MindMapTheme {
    return this.currentTheme;
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 销毁样式管理器
   */
  destroy(): void {
    this.isInitialized = false;
    console.log('✅ 样式管理器销毁完成');
  }
}