"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = (param)=>{\n    let { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, className = \"\", config = {} } = param;\n    _s();\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!containerRef.current) {\n            setRenderError(\"Container element not found\");\n            return;\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange,\n                onRenderComplete\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"Failed to initialize MindMap:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _selectionManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap();\n            }, 100);\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading,\n        updateMindMapData\n    ]);\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeMindMap,\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width,\n            height,\n            background: \"#1a1a1a\",\n            border: \"1px solid var(--color-primary-green, #8FBC8F)\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 2px 8px rgba(143, 188, 143, 0.3)\"\n        },\n        children: !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: \"absolute\",\n                top: \"10px\",\n                right: \"10px\",\n                background: \"rgba(143, 188, 143, 0.9)\",\n                color: \"#fff\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontSize: \"12px\",\n                zIndex: 100,\n                pointerEvents: \"none\",\n                fontFamily: \"var(--font-family-handwritten)\"\n            },\n            children: \"\\uD83D\\uDCA1 右键长按可框选多个节点\"\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 289,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MindMapRenderer, \"WsC+8anm46mIX4UTf2kk6BEDs5Y=\");\n_c = MindMapRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c;\n$RefreshReg$(_c, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});