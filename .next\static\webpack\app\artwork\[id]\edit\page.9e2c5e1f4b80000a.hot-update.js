"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleManager: function() { return /* binding */ styleManager; }\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/components/MindMapRenderer/utils/index.ts\");\n/**\r\n * StyleManager - SimpleMindMap 样式管理器\r\n * 负责主题管理、深色主题配置和自定义样式应用\r\n */ \nclass StyleManagerImpl {\n    /**\r\n   * 初始化样式管理器\r\n   */ initialize(mindMap) {\n        try {\n            this.mindMap = mindMap;\n            // 应用默认深色主题\n            this.applyTheme(this.currentTheme);\n            // 注入自定义CSS样式\n            this.injectCustomStyles();\n            this.isApplied = true;\n            console.log(\"StyleManager initialized successfully\");\n        } catch (error) {\n            const mindMapError = new MindMapError(MindMapErrorType.THEME_APPLICATION_FAILED, \"Failed to initialize style manager: \".concat(error instanceof Error ? error.message : \"Unknown error\"), error instanceof Error ? error : undefined);\n            console.error(\"Failed to initialize style manager:\", mindMapError);\n            throw mindMapError;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ applyTheme(theme) {\n        if (!this.mindMap) {\n            console.warn(\"SimpleMindMap instance is not available\");\n            return;\n        }\n        const applyThemeInternal = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(()=>{\n            this.currentTheme = theme;\n            if (theme === \"dark\") {\n                const darkThemeConfig = this.getDarkThemeConfig();\n                this.mindMap.setTheme(\"default\"); // 先设置基础主题\n                // 应用深色主题配置\n                this.applyThemeConfig(darkThemeConfig);\n            } else {\n                // 应用其他主题\n                this.mindMap.setTheme(theme);\n            }\n            console.log('Theme \"'.concat(theme, '\" applied successfully'));\n        }, undefined, \"theme application\");\n        applyThemeInternal();\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式 - 金色高亮\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#FFD700\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 4,\n                fontSize: 13,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#000000\",\n                padding: [\n                    6,\n                    10\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 0.95\n            },\n            // 展开按钮样式 - 金色\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#FFD700\",\n                fillColor: \"#0a0a0a\",\n                strokeColor: \"#FFD700\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式 - 金色系\n            associativeLine: {\n                strokeColor: \"#DAA520\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式 - 亮金色\n            activeNodeStyle: {\n                strokeColor: \"#FFFF00\",\n                strokeWidth: 4,\n                fillColor: \"rgba(255, 215, 0, 0.15)\"\n            },\n            // 悬停状态样式 - 金色光晕\n            hoverNodeStyle: {\n                fillColor: \"rgba(255, 215, 0, 0.1)\",\n                strokeColor: \"#FFA500\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 应用主题配置\r\n   */ applyThemeConfig(config) {\n        if (!this.mindMap) return;\n        try {\n            // 更新思维导图的主题配置\n            Object.keys(config).forEach((key)=>{\n                if (key !== \"root\" && key !== \"second\" && key !== \"node\" && key !== \"generalization\") {\n                    // 应用全局样式\n                    if (this.mindMap.themeConfig) {\n                        this.mindMap.themeConfig[key] = config[key];\n                    }\n                }\n            });\n            // 应用节点层级样式\n            if (this.mindMap.themeConfig) {\n                this.mindMap.themeConfig.root = {\n                    ...this.mindMap.themeConfig.root,\n                    ...config.root\n                };\n                this.mindMap.themeConfig.second = {\n                    ...this.mindMap.themeConfig.second,\n                    ...config.second\n                };\n                this.mindMap.themeConfig.node = {\n                    ...this.mindMap.themeConfig.node,\n                    ...config.node\n                };\n                this.mindMap.themeConfig.generalization = {\n                    ...this.mindMap.themeConfig.generalization,\n                    ...config.generalization\n                };\n            }\n            // 触发重新渲染\n            this.mindMap.render();\n        } catch (error) {\n            console.error(\"Error applying theme config:\", error);\n        }\n    }\n    /**\r\n   * 获取指定主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 应用自定义样式\r\n   */ applyCustomStyles(styles) {\n        try {\n            // 合并自定义样式\n            this.customStyles = {\n                ...this.customStyles,\n                ...styles\n            };\n            // 验证样式值\n            const validatedStyles = this.validateStyles(this.customStyles);\n            // 应用验证后的样式\n            this.applyValidatedStyles(validatedStyles);\n            console.log(\"Custom styles applied successfully\");\n        } catch (error) {\n            console.error(\"Error applying custom styles:\", error);\n        }\n    }\n    /**\r\n   * 验证样式值\r\n   */ validateStyles(styles) {\n        const validatedStyles = {};\n        Object.keys(styles).forEach((key)=>{\n            const value = styles[key];\n            // 验证颜色值\n            if (key.includes(\"Color\") || key.includes(\"color\")) {\n                if ((0,_utils__WEBPACK_IMPORTED_MODULE_0__.isValidColor)(value)) {\n                    validatedStyles[key] = value;\n                } else {\n                    console.warn(\"Invalid color value for \".concat(key, \": \").concat(value));\n                }\n            } else if (typeof value === \"number\" && !isNaN(value)) {\n                validatedStyles[key] = value;\n            } else if (typeof value === \"string\" && value.length > 0) {\n                validatedStyles[key] = value;\n            } else if (typeof value === \"object\" && value !== null) {\n                validatedStyles[key] = value;\n            }\n        });\n        return validatedStyles;\n    }\n    /**\r\n   * 应用验证后的样式\r\n   */ applyValidatedStyles(styles) {\n        if (!this.mindMap || !this.mindMap.themeConfig) return;\n        // 递归更新主题配置\n        const updateConfig = (target, source)=>{\n            Object.keys(source).forEach((key)=>{\n                if (typeof source[key] === \"object\" && source[key] !== null && !Array.isArray(source[key])) {\n                    if (!target[key]) target[key] = {};\n                    updateConfig(target[key], source[key]);\n                } else {\n                    target[key] = source[key];\n                }\n            });\n        };\n        updateConfig(this.mindMap.themeConfig, styles);\n        // 触发重新渲染\n        this.mindMap.render();\n    }\n    /**\r\n   * 注入自定义CSS样式\r\n   */ injectCustomStyles() {\n        const styleId = \"mind-map-custom-styles\";\n        // 检查是否已存在\n        if (document.getElementById(styleId)) {\n            return;\n        }\n        const style = document.createElement(\"style\");\n        style.id = styleId;\n        style.textContent = '\\n      /* MindMapRenderer 自定义样式 */\\n      .mind-map-container {\\n        background: #1a1a2e;\\n        border-radius: 8px;\\n        overflow: hidden;\\n      }\\n      \\n      .mind-map-container svg {\\n        background: #1a1a2e;\\n      }\\n      \\n      /* 选中节点样式 */\\n      .mind-map-selected {\\n        filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));\\n      }\\n      \\n      /* 悬停效果 */\\n      .mind-map-node:hover {\\n        transition: all 0.2s ease-in-out;\\n      }\\n      \\n      /* 连线动画 */\\n      .mind-map-line {\\n        transition: stroke-width 0.2s ease-in-out;\\n      }\\n      \\n      .mind-map-line:hover {\\n        stroke-width: 3px !important;\\n      }\\n      \\n      /* 展开按钮样式 */\\n      .mind-map-expand-btn {\\n        cursor: pointer;\\n        transition: all 0.2s ease-in-out;\\n      }\\n      \\n      .mind-map-expand-btn:hover {\\n        transform: scale(1.1);\\n        filter: brightness(1.2);\\n      }\\n      \\n      /* 文本编辑样式 */\\n      .mind-map-text-edit {\\n        background: #0f172a;\\n        color: #cbd5e1;\\n        border: 2px solid #4a90e2;\\n        border-radius: 4px;\\n        font-family: \"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\\n        outline: none;\\n      }\\n      \\n      .mind-map-text-edit:focus {\\n        border-color: #60a5fa;\\n        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n      }\\n      \\n      /* 滚动条样式 */\\n      .mind-map-container ::-webkit-scrollbar {\\n        width: 8px;\\n        height: 8px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-track {\\n        background: rgba(255, 255, 255, 0.1);\\n        border-radius: 4px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-thumb {\\n        background: rgba(255, 255, 255, 0.3);\\n        border-radius: 4px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-thumb:hover {\\n        background: rgba(255, 255, 255, 0.5);\\n      }\\n      \\n      /* 加载动画 */\\n      .mind-map-loading {\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        height: 100%;\\n        background: #1a1a2e;\\n        color: #cbd5e1;\\n        font-family: \"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\\n      }\\n      \\n      .mind-map-loading::after {\\n        content: \\'\\';\\n        width: 32px;\\n        height: 32px;\\n        margin-left: 8px;\\n        border: 3px solid rgba(59, 130, 246, 0.3);\\n        border-top-color: #4a90e2;\\n        border-radius: 50%;\\n        animation: mind-map-spin 1s linear infinite;\\n      }\\n      \\n      @keyframes mind-map-spin {\\n        to {\\n          transform: rotate(360deg);\\n        }\\n      }\\n    ';\n        document.head.appendChild(style);\n        console.log(\"Custom styles injected\");\n    }\n    /**\r\n   * 重置样式\r\n   */ resetStyles() {\n        try {\n            this.customStyles = {};\n            // 重新应用当前主题\n            this.applyTheme(this.currentTheme);\n            console.log(\"Styles reset successfully\");\n        } catch (error) {\n            console.error(\"Error resetting styles:\", error);\n        }\n    }\n    /**\r\n   * 更新节点样式\r\n   */ updateNodeStyle(nodeLevel, styles) {\n        try {\n            if (!this.mindMap || !this.mindMap.themeConfig) return;\n            const validatedStyles = this.validateStyles(styles);\n            this.mindMap.themeConfig[nodeLevel] = {\n                ...this.mindMap.themeConfig[nodeLevel],\n                ...validatedStyles\n            };\n            this.mindMap.render();\n            console.log(\"\".concat(nodeLevel, \" node style updated\"));\n        } catch (error) {\n            console.error(\"Error updating \".concat(nodeLevel, \" node style:\"), error);\n        }\n    }\n    /**\r\n   * 获取当前样式配置\r\n   */ getCurrentStyles() {\n        var _this_mindMap;\n        return {\n            theme: this.currentTheme,\n            customStyles: {\n                ...this.customStyles\n            },\n            themeConfig: ((_this_mindMap = this.mindMap) === null || _this_mindMap === void 0 ? void 0 : _this_mindMap.themeConfig) || {}\n        };\n    }\n    /**\r\n   * 销毁样式管理器\r\n   */ destroy() {\n        try {\n            // 移除注入的样式\n            const styleElement = document.getElementById(\"mind-map-custom-styles\");\n            if (styleElement) {\n                styleElement.remove();\n            }\n            // 重置状态\n            this.mindMap = null;\n            this.customStyles = {};\n            this.isApplied = false;\n            console.log(\"StyleManager destroyed\");\n        } catch (error) {\n            console.error(\"Error destroying style manager:\", error);\n        }\n    }\n    constructor(){\n        this.currentTheme = \"dark\";\n        this.customStyles = {};\n        this.mindMap = null;\n        this.isApplied = false;\n    }\n}\n// 导出单例实例\nconst styleManager = new StyleManagerImpl();\n/* harmony default export */ __webpack_exports__[\"default\"] = (styleManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ })

});