/**
 * CoreManager - 核心管理器
 * 负责SimpleMindMap实例的创建、初始化和数据管理
 * 
 * 职责：
 * - SimpleMindMap实例生命周期管理
 * - 数据设置和更新
 * - 基础配置管理
 */

import { SimpleMindMapInstance, MindMapNode, MindMapConfig, DEFAULT_MINDMAP_CONFIG } from '../types';

export interface CoreManagerConfig {
  container: HTMLElement;
  data: MindMapNode;
  readonly: boolean;
  config: Partial<MindMapConfig>;
}

export class CoreManager {
  private mindMapInstance: SimpleMindMapInstance | null = null;
  private config: CoreManagerConfig;
  private isInitialized = false;

  constructor(config: CoreManagerConfig) {
    this.config = config;
  }

  /**
   * 初始化SimpleMindMap实例
   */
  async initialize(): Promise<SimpleMindMapInstance> {
    try {
      // 动态导入SimpleMindMap完整版（包含所有插件，特别是Drag插件）
      const { default: SimpleMindMap } = await import('simple-mind-map/full.js') as any;

      // 清理现有实例
      if (this.mindMapInstance) {
        this.mindMapInstance.destroy();
      }

      // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器
      const finalConfig: any = {
        ...DEFAULT_MINDMAP_CONFIG,
        ...this.config.config,
        el: this.config.container,
        data: this.config.data,
        readonly: this.config.readonly
        // 移除固定的width/height设置，让SimpleMindMap自动适应容器尺寸
      };

      // 创建实例
      this.mindMapInstance = new SimpleMindMap(finalConfig) as unknown as SimpleMindMapInstance;
      this.isInitialized = true;
      return this.mindMapInstance;

    } catch (error) {
      console.error('❌ SimpleMindMap初始化失败:', error);
      throw new Error(`Failed to initialize SimpleMindMap: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取SimpleMindMap实例
   */
  getInstance(): SimpleMindMapInstance | null {
    return this.mindMapInstance;
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized && this.mindMapInstance !== null;
  }

  /**
   * 设置数据
   */
  setData(data: MindMapNode): void {
    if (!this.mindMapInstance) {
      throw new Error('MindMap instance not initialized');
    }

    try {
      this.mindMapInstance.setData(data);
    } catch (error) {
      console.error('❌ 数据设置失败:', error);
      throw error;
    }
  }

  /**
   * 更新数据（性能优化版本）
   */
  updateData(data: MindMapNode): void {
    if (!this.mindMapInstance) {
      throw new Error('MindMap instance not initialized');
    }

    try {
      // 使用官方的updateData方法，性能更好
      if (typeof this.mindMapInstance.updateData === 'function') {
        this.mindMapInstance.updateData(data);
      } else {
        // 降级方案
        this.mindMapInstance.setData(data);
      }

    } catch (error) {
      console.error('❌ 数据更新失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据
   */
  getData(withConfig = false): any {
    if (!this.mindMapInstance) {
      throw new Error('MindMap instance not initialized');
    }

    try {
      return this.mindMapInstance.getData(withConfig);
    } catch (error) {
      console.error('❌ 获取数据失败:', error);
      throw error;
    }
  }

  /**
   * 容器尺寸变化后调整画布
   */
  resize(): void {
    if (!this.mindMapInstance) return;

    try {
      // 先更新容器位置和尺寸信息
      this.mindMapInstance.getElRectInfo();
      // 然后调整画布尺寸
      this.mindMapInstance.resize();

    } catch (error) {
      console.error('❌ 画布尺寸调整失败:', error);
    }
  }

  /**
   * 适应画布大小
   */
  fitView(): void {
    if (!this.mindMapInstance) return;

    try {
      // 使用官方API适应画布
      this.mindMapInstance.view.fit();
    } catch (error) {
      console.error('❌ 画布适应失败:', error);
    }
  }

  /**
   * 重置视图
   */
  resetView(): void {
    if (!this.mindMapInstance) return;

    try {
      this.mindMapInstance.view.reset();
    } catch (error) {
      console.error('❌ 视图重置失败:', error);
    }
  }

  /**
   * 坐标转换
   */
  toCanvasPosition(screenX: number, screenY: number): { x: number; y: number } {
    if (!this.mindMapInstance) {
      return { x: screenX, y: screenY };
    }

    try {
      return this.mindMapInstance.toPos(screenX, screenY);
    } catch (error) {
      console.error('❌ 坐标转换失败:', error);
      return { x: screenX, y: screenY };
    }
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    if (this.mindMapInstance) {
      try {
        this.mindMapInstance.destroy();
        console.log('✅ SimpleMindMap实例销毁完成');
      } catch (error) {
        console.error('❌ 销毁SimpleMindMap实例失败:', error);
      }
    }

    this.mindMapInstance = null;
    this.isInitialized = false;
  }
}