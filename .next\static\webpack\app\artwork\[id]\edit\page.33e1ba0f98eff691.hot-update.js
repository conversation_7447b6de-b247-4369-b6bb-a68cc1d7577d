"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleManager: function() { return /* binding */ StyleManager; },\n/* harmony export */   styleManager: function() { return /* binding */ styleManager; }\n/* harmony export */ });\n/**\r\n * StyleManager - 样式管理器\r\n * 负责SimpleMindMap主题和样式的统一管理\r\n * \r\n * 职责：\r\n * - 主题切换和配置\r\n * - 自定义样式应用\r\n * - 样式状态管理\r\n * \r\n * 设计原则：\r\n * - 官方API优先：使用SimpleMindMap官方主题API\r\n * - 简洁高效：最小化样式配置，避免过度自定义\r\n */ class StyleManager {\n    /**\r\n   * 初始化样式管理器\r\n   */ async initialize() {\n        try {\n            // 应用初始主题\n            await this.applyTheme(this.currentTheme);\n            this.isInitialized = true;\n            console.log(\"✅ 样式管理器初始化完成，主题:\", this.currentTheme);\n        } catch (error) {\n            console.error(\"❌ 样式管理器初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ async applyTheme(theme) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not available\");\n        }\n        try {\n            // 使用官方API设置主题\n            if (typeof this.mindMapInstance.setTheme === \"function\") {\n                this.mindMapInstance.setTheme(theme);\n            } else if (typeof this.mindMapInstance.setThemeConfig === \"function\") {\n                // 降级方案：使用主题配置API\n                const themeConfig = this.getThemeConfig(theme);\n                this.mindMapInstance.setThemeConfig(themeConfig, false);\n            }\n            this.currentTheme = theme;\n            console.log(\"✅ 主题应用成功: \".concat(theme));\n        } catch (error) {\n            console.error(\"❌ 主题应用失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式 - 金色高亮\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#FFD700\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 4,\n                fontSize: 13,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#000000\",\n                padding: [\n                    6,\n                    10\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 0.95\n            },\n            // 展开按钮样式 - 金色\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#FFD700\",\n                fillColor: \"#0a0a0a\",\n                strokeColor: \"#FFD700\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式 - 金色系\n            associativeLine: {\n                strokeColor: \"#DAA520\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式 - 亮金色\n            activeNodeStyle: {\n                strokeColor: \"#FFFF00\",\n                strokeWidth: 4,\n                fillColor: \"rgba(255, 215, 0, 0.15)\"\n            },\n            // 悬停状态样式 - 金色光晕\n            hoverNodeStyle: {\n                fillColor: \"rgba(255, 215, 0, 0.1)\",\n                strokeColor: \"#FFA500\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 应用主题配置\r\n   */ applyThemeConfig(config) {\n        if (!this.mindMap) return;\n        try {\n            // 更新思维导图的主题配置\n            Object.keys(config).forEach((key)=>{\n                if (key !== \"root\" && key !== \"second\" && key !== \"node\" && key !== \"generalization\") {\n                    // 应用全局样式\n                    if (this.mindMap.themeConfig) {\n                        this.mindMap.themeConfig[key] = config[key];\n                    }\n                }\n            });\n            // 应用节点层级样式\n            if (this.mindMap.themeConfig) {\n                this.mindMap.themeConfig.root = {\n                    ...this.mindMap.themeConfig.root,\n                    ...config.root\n                };\n                this.mindMap.themeConfig.second = {\n                    ...this.mindMap.themeConfig.second,\n                    ...config.second\n                };\n                this.mindMap.themeConfig.node = {\n                    ...this.mindMap.themeConfig.node,\n                    ...config.node\n                };\n                this.mindMap.themeConfig.generalization = {\n                    ...this.mindMap.themeConfig.generalization,\n                    ...config.generalization\n                };\n            }\n            // 触发重新渲染\n            this.mindMap.render();\n        } catch (error) {\n            console.error(\"Error applying theme config:\", error);\n        }\n    }\n    /**\r\n   * 获取指定主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 应用自定义样式\r\n   */ applyCustomStyles(styles) {\n        try {\n            // 合并自定义样式\n            this.customStyles = {\n                ...this.customStyles,\n                ...styles\n            };\n            // 验证样式值\n            const validatedStyles = this.validateStyles(this.customStyles);\n            // 应用验证后的样式\n            this.applyValidatedStyles(validatedStyles);\n            console.log(\"Custom styles applied successfully\");\n        } catch (error) {\n            console.error(\"Error applying custom styles:\", error);\n        }\n    }\n    /**\r\n   * 验证样式值\r\n   */ validateStyles(styles) {\n        const validatedStyles = {};\n        Object.keys(styles).forEach((key)=>{\n            const value = styles[key];\n            // 验证颜色值\n            if (key.includes(\"Color\") || key.includes(\"color\")) {\n                if (isValidColor(value)) {\n                    validatedStyles[key] = value;\n                } else {\n                    console.warn(\"Invalid color value for \".concat(key, \": \").concat(value));\n                }\n            } else if (typeof value === \"number\" && !isNaN(value)) {\n                validatedStyles[key] = value;\n            } else if (typeof value === \"string\" && value.length > 0) {\n                validatedStyles[key] = value;\n            } else if (typeof value === \"object\" && value !== null) {\n                validatedStyles[key] = value;\n            }\n        });\n        return validatedStyles;\n    }\n    /**\r\n   * 应用验证后的样式\r\n   */ applyValidatedStyles(styles) {\n        if (!this.mindMap || !this.mindMap.themeConfig) return;\n        // 递归更新主题配置\n        const updateConfig = (target, source)=>{\n            Object.keys(source).forEach((key)=>{\n                if (typeof source[key] === \"object\" && source[key] !== null && !Array.isArray(source[key])) {\n                    if (!target[key]) target[key] = {};\n                    updateConfig(target[key], source[key]);\n                } else {\n                    target[key] = source[key];\n                }\n            });\n        };\n        updateConfig(this.mindMap.themeConfig, styles);\n        // 触发重新渲染\n        this.mindMap.render();\n    }\n    /**\r\n   * 注入自定义CSS样式\r\n   */ injectCustomStyles() {\n        const styleId = \"mind-map-custom-styles\";\n        // 检查是否已存在\n        if (document.getElementById(styleId)) {\n            return;\n        }\n        const style = document.createElement(\"style\");\n        style.id = styleId;\n        style.textContent = '\\n      /* MindMapRenderer 自定义样式 */\\n      .mind-map-container {\\n        background: #1a1a2e;\\n        border-radius: 8px;\\n        overflow: hidden;\\n      }\\n      \\n      .mind-map-container svg {\\n        background: #1a1a2e;\\n      }\\n      \\n      /* 选中节点样式 */\\n      .mind-map-selected {\\n        filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));\\n      }\\n      \\n      /* 悬停效果 */\\n      .mind-map-node:hover {\\n        transition: all 0.2s ease-in-out;\\n      }\\n      \\n      /* 连线动画 */\\n      .mind-map-line {\\n        transition: stroke-width 0.2s ease-in-out;\\n      }\\n      \\n      .mind-map-line:hover {\\n        stroke-width: 3px !important;\\n      }\\n      \\n      /* 展开按钮样式 */\\n      .mind-map-expand-btn {\\n        cursor: pointer;\\n        transition: all 0.2s ease-in-out;\\n      }\\n      \\n      .mind-map-expand-btn:hover {\\n        transform: scale(1.1);\\n        filter: brightness(1.2);\\n      }\\n      \\n      /* 文本编辑样式 */\\n      .mind-map-text-edit {\\n        background: #0f172a;\\n        color: #cbd5e1;\\n        border: 2px solid #4a90e2;\\n        border-radius: 4px;\\n        font-family: \"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\\n        outline: none;\\n      }\\n      \\n      .mind-map-text-edit:focus {\\n        border-color: #60a5fa;\\n        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n      }\\n      \\n      /* 滚动条样式 */\\n      .mind-map-container ::-webkit-scrollbar {\\n        width: 8px;\\n        height: 8px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-track {\\n        background: rgba(255, 255, 255, 0.1);\\n        border-radius: 4px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-thumb {\\n        background: rgba(255, 255, 255, 0.3);\\n        border-radius: 4px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-thumb:hover {\\n        background: rgba(255, 255, 255, 0.5);\\n      }\\n      \\n      /* 加载动画 */\\n      .mind-map-loading {\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        height: 100%;\\n        background: #1a1a2e;\\n        color: #cbd5e1;\\n        font-family: \"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\\n      }\\n      \\n      .mind-map-loading::after {\\n        content: \\'\\';\\n        width: 32px;\\n        height: 32px;\\n        margin-left: 8px;\\n        border: 3px solid rgba(59, 130, 246, 0.3);\\n        border-top-color: #4a90e2;\\n        border-radius: 50%;\\n        animation: mind-map-spin 1s linear infinite;\\n      }\\n      \\n      @keyframes mind-map-spin {\\n        to {\\n          transform: rotate(360deg);\\n        }\\n      }\\n    ';\n        document.head.appendChild(style);\n        console.log(\"Custom styles injected\");\n    }\n    /**\r\n   * 重置样式\r\n   */ resetStyles() {\n        try {\n            this.customStyles = {};\n            // 重新应用当前主题\n            this.applyTheme(this.currentTheme);\n            console.log(\"Styles reset successfully\");\n        } catch (error) {\n            console.error(\"Error resetting styles:\", error);\n        }\n    }\n    /**\r\n   * 更新节点样式\r\n   */ updateNodeStyle(nodeLevel, styles) {\n        try {\n            if (!this.mindMap || !this.mindMap.themeConfig) return;\n            const validatedStyles = this.validateStyles(styles);\n            this.mindMap.themeConfig[nodeLevel] = {\n                ...this.mindMap.themeConfig[nodeLevel],\n                ...validatedStyles\n            };\n            this.mindMap.render();\n            console.log(\"\".concat(nodeLevel, \" node style updated\"));\n        } catch (error) {\n            console.error(\"Error updating \".concat(nodeLevel, \" node style:\"), error);\n        }\n    }\n    /**\r\n   * 获取当前样式配置\r\n   */ getCurrentStyles() {\n        var _this_mindMap;\n        return {\n            theme: this.currentTheme,\n            customStyles: {\n                ...this.customStyles\n            },\n            themeConfig: ((_this_mindMap = this.mindMap) === null || _this_mindMap === void 0 ? void 0 : _this_mindMap.themeConfig) || {}\n        };\n    }\n    /**\r\n   * 销毁样式管理器\r\n   */ destroy() {\n        try {\n            // 移除注入的样式\n            const styleElement = document.getElementById(\"mind-map-custom-styles\");\n            if (styleElement) {\n                styleElement.remove();\n            }\n            // 重置状态\n            this.mindMap = null;\n            this.customStyles = {};\n            this.isApplied = false;\n            console.log(\"StyleManager destroyed\");\n        } catch (error) {\n            console.error(\"Error destroying style manager:\", error);\n        }\n    }\n    constructor(mindMapInstance, theme = \"dark\"){\n        this.isInitialized = false;\n        this.mindMapInstance = mindMapInstance;\n        this.currentTheme = theme;\n    }\n}\n// 导出单例实例\nconst styleManager = new StyleManagerImpl();\n/* harmony default export */ __webpack_exports__[\"default\"] = (styleManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ })

});