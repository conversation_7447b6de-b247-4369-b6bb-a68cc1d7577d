"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ResizableLayout/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/ResizableLayout/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * 可调整大小的三栏布局组件\r\n * 支持左栏和右栏收起、拖拽调整宽度\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst ResizableLayout = (param)=>{\n    let { leftPanel, centerPanel, rightPanel, className = \"\", initialLeftWidth = 20, initialRightWidth = 30, minLeftWidth = 15, minRightWidth = 20, minCenterWidth = 30, onLayoutChange } = param;\n    _s();\n    // 布局状态\n    const [leftWidth, setLeftWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLeftWidth);\n    const [rightWidth, setRightWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialRightWidth);\n    const [leftCollapsed, setLeftCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightCollapsed, setRightCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragType, setDragType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragStartX = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const dragStartLeftWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const dragStartRightWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // 计算实际宽度\n    const actualLeftWidth = leftCollapsed ? 0 : leftWidth;\n    const actualRightWidth = rightCollapsed ? 0 : rightWidth;\n    const actualCenterWidth = 100 - actualLeftWidth - actualRightWidth;\n    // 处理左栏收起/展开\n    const toggleLeftPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newCollapsed = !leftCollapsed;\n        setLeftCollapsed(newCollapsed);\n        if (onLayoutChange) {\n            onLayoutChange(leftWidth, rightWidth, newCollapsed, rightCollapsed);\n        }\n    }, [\n        leftCollapsed,\n        leftWidth,\n        rightWidth,\n        rightCollapsed,\n        onLayoutChange\n    ]);\n    // 处理右栏收起/展开\n    const toggleRightPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newCollapsed = !rightCollapsed;\n        setRightCollapsed(newCollapsed);\n        if (onLayoutChange) {\n            onLayoutChange(leftWidth, rightWidth, leftCollapsed, newCollapsed);\n        }\n    }, [\n        rightCollapsed,\n        leftWidth,\n        rightWidth,\n        leftCollapsed,\n        onLayoutChange\n    ]);\n    // 使用ref来存储当前状态，避免闭包问题\n    const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        leftWidth,\n        rightWidth,\n        leftCollapsed,\n        rightCollapsed,\n        dragType: null,\n        isDragging: false\n    });\n    // 更新ref中的状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        stateRef.current = {\n            leftWidth,\n            rightWidth,\n            leftCollapsed,\n            rightCollapsed,\n            dragType,\n            isDragging\n        };\n    }, [\n        leftWidth,\n        rightWidth,\n        leftCollapsed,\n        rightCollapsed,\n        dragType,\n        isDragging\n    ]);\n    // 处理拖拽移动\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (!containerRef.current || !stateRef.current.isDragging) return;\n        const containerRect = containerRef.current.getBoundingClientRect();\n        const containerWidth = containerRect.width;\n        const deltaX = e.clientX - dragStartX.current;\n        const deltaPercent = deltaX / containerWidth * 100;\n        const currentDragType = stateRef.current.dragType;\n        if (!currentDragType) return;\n        if (currentDragType === \"left\") {\n            // 拖拽左侧分割线\n            const currentActualRightWidth = stateRef.current.rightCollapsed ? 0 : stateRef.current.rightWidth;\n            const newLeftWidth = Math.max(minLeftWidth, Math.min(100 - currentActualRightWidth - minCenterWidth, dragStartLeftWidth.current + deltaPercent));\n            setLeftWidth(newLeftWidth);\n        } else if (currentDragType === \"right\") {\n            // 拖拽右侧分割线\n            const currentActualLeftWidth = stateRef.current.leftCollapsed ? 0 : stateRef.current.leftWidth;\n            const newRightWidth = Math.max(minRightWidth, Math.min(100 - currentActualLeftWidth - minCenterWidth, dragStartRightWidth.current - deltaPercent));\n            setRightWidth(newRightWidth);\n        }\n    }, [\n        minLeftWidth,\n        minRightWidth,\n        minCenterWidth\n    ]);\n    // 结束拖拽\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(false);\n        setDragType(null);\n        // 移除全局事件监听\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"mouseup\", handleMouseUp);\n        document.body.style.cursor = \"\";\n        document.body.style.userSelect = \"\";\n        if (onLayoutChange) {\n            const current = stateRef.current;\n            onLayoutChange(current.leftWidth, current.rightWidth, current.leftCollapsed, current.rightCollapsed);\n        }\n    }, [\n        onLayoutChange,\n        handleMouseMove\n    ]);\n    // 开始拖拽\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, type)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragging(true);\n        setDragType(type);\n        dragStartX.current = e.clientX;\n        dragStartLeftWidth.current = leftWidth;\n        dragStartRightWidth.current = rightWidth;\n        // 添加全局鼠标事件监听\n        document.addEventListener(\"mousemove\", handleMouseMove);\n        document.addEventListener(\"mouseup\", handleMouseUp);\n        document.body.style.cursor = \"col-resize\";\n        document.body.style.userSelect = \"none\";\n        console.log(\"\\uD83D\\uDDB1️ 开始拖拽:\", type, {\n            leftWidth,\n            rightWidth\n        });\n    }, [\n        leftWidth,\n        rightWidth,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    // 清理事件监听器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            document.removeEventListener(\"mousemove\", handleMouseMove);\n            document.removeEventListener(\"mouseup\", handleMouseUp);\n            document.body.style.cursor = \"\";\n            document.body.style.userSelect = \"\";\n        };\n    }, [\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"flex h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-b from-gray-900 to-gray-800 border-r border-amber-500/30 flex flex-col transition-all duration-300 ease-in-out \".concat(leftCollapsed ? \"w-0 overflow-hidden\" : \"\"),\n                style: {\n                    width: leftCollapsed ? \"0%\" : \"\".concat(leftWidth, \"%\"),\n                    minWidth: leftCollapsed ? \"0px\" : \"\".concat(minLeftWidth, \"%\")\n                },\n                children: leftPanel\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleLeftPanel,\n                        className: \"absolute z-10 w-6 h-12 bg-gradient-to-r from-amber-600/20 to-amber-500/20 hover:from-amber-600/30 hover:to-amber-500/30 border border-amber-500/50 rounded-r-md transition-all duration-200 flex items-center justify-center group shadow-lg \".concat(leftCollapsed ? \"-left-0\" : \"-left-3\"),\n                        title: leftCollapsed ? \"展开文件树\" : \"收起文件树\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"12\",\n                            height: \"12\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"currentColor\",\n                            className: \"text-amber-400 transition-transform duration-200 \".concat(leftCollapsed ? \"rotate-0\" : \"rotate-180\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined),\n                    !leftCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-full bg-transparent hover:bg-amber-500/30 cursor-col-resize transition-colors duration-200 relative group\",\n                        onMouseDown: (e)=>handleMouseDown(e, \"left\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-y-0 left-0 w-1 bg-gradient-to-b from-amber-500/10 via-amber-500/30 to-amber-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-12 bg-gradient-to-b from-amber-500/20 to-amber-500/10 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center shadow-md border border-amber-500/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-0.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-b from-gray-800 to-gray-900 border-r border-amber-500/20 flex-1\",\n                style: {\n                    width: \"\".concat(actualCenterWidth, \"%\"),\n                    minWidth: \"\".concat(minCenterWidth, \"%\")\n                },\n                children: centerPanel\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center\",\n                children: [\n                    !rightCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-full bg-transparent hover:bg-amber-500/30 cursor-col-resize transition-colors duration-200 relative group\",\n                        onMouseDown: (e)=>handleMouseDown(e, \"right\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-y-0 left-0 w-1 bg-gradient-to-b from-amber-500/10 via-amber-500/30 to-amber-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-12 bg-gradient-to-b from-amber-500/20 to-amber-500/10 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center shadow-md border border-amber-500/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-0.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleRightPanel,\n                        className: \"absolute z-10 w-6 h-12 bg-gradient-to-r from-amber-500/20 to-amber-600/20 hover:from-amber-500/30 hover:to-amber-600/30 border border-amber-500/50 rounded-l-md transition-all duration-200 flex items-center justify-center group shadow-lg \".concat(rightCollapsed ? \"-right-0\" : \"-right-3\"),\n                        title: rightCollapsed ? \"展开右侧面板\" : \"收起右侧面板\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"12\",\n                            height: \"12\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"currentColor\",\n                            className: \"text-amber-400 transition-transform duration-200 \".concat(rightCollapsed ? \"rotate-180\" : \"rotate-0\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            !rightCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-b from-gray-800 to-gray-900 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    width: \"\".concat(rightWidth, \"%\"),\n                    minWidth: \"\".concat(minRightWidth, \"%\")\n                },\n                children: rightPanel\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, undefined),\n            isDragging && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 cursor-col-resize\",\n                style: {\n                    pointerEvents: \"none\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResizableLayout, \"TCoEAVdsguYWSYNx4dQI7oppES4=\");\n_c = ResizableLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ResizableLayout);\nvar _c;\n$RefreshReg$(_c, \"ResizableLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ResizableLayout/index.tsx\n"));

/***/ })

});