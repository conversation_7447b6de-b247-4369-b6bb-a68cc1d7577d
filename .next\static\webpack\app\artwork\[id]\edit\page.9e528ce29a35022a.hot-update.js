"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ResizableLayout/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/ResizableLayout/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * 可调整大小的三栏布局组件\r\n * 支持左栏和右栏收起、拖拽调整宽度\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst ResizableLayout = (param)=>{\n    let { leftPanel, centerPanel, rightPanel, className = \"\", initialLeftWidth = 20, initialRightWidth = 30, minLeftWidth = 15, minRightWidth = 20, minCenterWidth = 30, onLayoutChange } = param;\n    _s();\n    // 布局状态\n    const [leftWidth, setLeftWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLeftWidth);\n    const [rightWidth, setRightWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialRightWidth);\n    const [leftCollapsed, setLeftCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightCollapsed, setRightCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragType, setDragType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragStartX = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const dragStartLeftWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const dragStartRightWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // 计算实际宽度\n    const actualLeftWidth = leftCollapsed ? 0 : leftWidth;\n    const actualRightWidth = rightCollapsed ? 0 : rightWidth;\n    const actualCenterWidth = 100 - actualLeftWidth - actualRightWidth;\n    // 处理左栏收起/展开\n    const toggleLeftPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newCollapsed = !leftCollapsed;\n        setLeftCollapsed(newCollapsed);\n        if (onLayoutChange) {\n            onLayoutChange(leftWidth, rightWidth, newCollapsed, rightCollapsed);\n        }\n    }, [\n        leftCollapsed,\n        leftWidth,\n        rightWidth,\n        rightCollapsed,\n        onLayoutChange\n    ]);\n    // 处理右栏收起/展开\n    const toggleRightPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newCollapsed = !rightCollapsed;\n        setRightCollapsed(newCollapsed);\n        if (onLayoutChange) {\n            onLayoutChange(leftWidth, rightWidth, leftCollapsed, newCollapsed);\n        }\n    }, [\n        rightCollapsed,\n        leftWidth,\n        rightWidth,\n        leftCollapsed,\n        onLayoutChange\n    ]);\n    // 使用ref来存储当前状态，避免闭包问题\n    const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        leftWidth,\n        rightWidth,\n        leftCollapsed,\n        rightCollapsed,\n        dragType: null,\n        isDragging: false\n    });\n    // 更新ref中的状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        stateRef.current = {\n            leftWidth,\n            rightWidth,\n            leftCollapsed,\n            rightCollapsed,\n            dragType,\n            isDragging\n        };\n    }, [\n        leftWidth,\n        rightWidth,\n        leftCollapsed,\n        rightCollapsed,\n        dragType,\n        isDragging\n    ]);\n    // 处理拖拽移动\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (!containerRef.current || !stateRef.current.isDragging) return;\n        const containerRect = containerRef.current.getBoundingClientRect();\n        const containerWidth = containerRect.width;\n        const deltaX = e.clientX - dragStartX.current;\n        const deltaPercent = deltaX / containerWidth * 100;\n        const currentDragType = stateRef.current.dragType;\n        if (!currentDragType) return;\n        if (currentDragType === \"left\") {\n            // 拖拽左侧分割线\n            const currentActualRightWidth = stateRef.current.rightCollapsed ? 0 : stateRef.current.rightWidth;\n            const newLeftWidth = Math.max(minLeftWidth, Math.min(100 - currentActualRightWidth - minCenterWidth, dragStartLeftWidth.current + deltaPercent));\n            setLeftWidth(newLeftWidth);\n        } else if (currentDragType === \"right\") {\n            // 拖拽右侧分割线\n            const currentActualLeftWidth = stateRef.current.leftCollapsed ? 0 : stateRef.current.leftWidth;\n            const newRightWidth = Math.max(minRightWidth, Math.min(100 - currentActualLeftWidth - minCenterWidth, dragStartRightWidth.current - deltaPercent));\n            setRightWidth(newRightWidth);\n        }\n    }, [\n        minLeftWidth,\n        minRightWidth,\n        minCenterWidth\n    ]);\n    // 结束拖拽\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(false);\n        setDragType(null);\n        // 移除全局事件监听\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"mouseup\", handleMouseUp);\n        document.body.style.cursor = \"\";\n        document.body.style.userSelect = \"\";\n        if (onLayoutChange) {\n            const current = stateRef.current;\n            onLayoutChange(current.leftWidth, current.rightWidth, current.leftCollapsed, current.rightCollapsed);\n        }\n    }, [\n        onLayoutChange,\n        handleMouseMove\n    ]);\n    // 开始拖拽\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, type)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragging(true);\n        setDragType(type);\n        dragStartX.current = e.clientX;\n        dragStartLeftWidth.current = leftWidth;\n        dragStartRightWidth.current = rightWidth;\n        // 添加全局鼠标事件监听\n        document.addEventListener(\"mousemove\", handleMouseMove);\n        document.addEventListener(\"mouseup\", handleMouseUp);\n        document.body.style.cursor = \"col-resize\";\n        document.body.style.userSelect = \"none\";\n        console.log(\"\\uD83D\\uDDB1️ 开始拖拽:\", type, {\n            leftWidth,\n            rightWidth\n        });\n    }, [\n        leftWidth,\n        rightWidth,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    // 清理事件监听器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            document.removeEventListener(\"mousemove\", handleMouseMove);\n            document.removeEventListener(\"mouseup\", handleMouseUp);\n            document.body.style.cursor = \"\";\n            document.body.style.userSelect = \"\";\n        };\n    }, [\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"flex h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-b from-gray-900 to-gray-800 border-r border-amber-500/30 flex flex-col transition-all duration-300 ease-in-out \".concat(leftCollapsed ? \"w-0 overflow-hidden\" : \"\"),\n                style: {\n                    width: leftCollapsed ? \"0%\" : \"\".concat(leftWidth, \"%\"),\n                    minWidth: leftCollapsed ? \"0px\" : \"\".concat(minLeftWidth, \"%\")\n                },\n                children: leftPanel\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleLeftPanel,\n                        className: \"absolute z-10 w-6 h-12 bg-gradient-to-r from-amber-600/20 to-amber-500/20 hover:from-amber-600/30 hover:to-amber-500/30 border border-amber-500/50 rounded-r-md transition-all duration-200 flex items-center justify-center group shadow-lg \".concat(leftCollapsed ? \"-left-0\" : \"-left-3\"),\n                        title: leftCollapsed ? \"展开文件树\" : \"收起文件树\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"12\",\n                            height: \"12\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"currentColor\",\n                            className: \"text-amber-400 transition-transform duration-200 \".concat(leftCollapsed ? \"rotate-0\" : \"rotate-180\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined),\n                    !leftCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-full bg-transparent hover:bg-amber-500/30 cursor-col-resize transition-colors duration-200 relative group\",\n                        onMouseDown: (e)=>handleMouseDown(e, \"left\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-y-0 left-0 w-1 bg-gradient-to-b from-amber-500/10 via-amber-500/30 to-amber-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-12 bg-gradient-to-b from-amber-500/20 to-amber-500/10 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center shadow-md border border-amber-500/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-0.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-b from-gray-800 to-gray-900 border-r border-amber-500/20 flex-1\",\n                style: {\n                    width: \"\".concat(actualCenterWidth, \"%\"),\n                    minWidth: \"\".concat(minCenterWidth, \"%\")\n                },\n                children: centerPanel\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center\",\n                children: [\n                    !rightCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-full bg-transparent hover:bg-amber-500/30 cursor-col-resize transition-colors duration-200 relative group\",\n                        onMouseDown: (e)=>handleMouseDown(e, \"right\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-y-0 left-0 w-1 bg-gradient-to-b from-amber-500/10 via-amber-500/30 to-amber-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-12 bg-gradient-to-b from-amber-500/20 to-amber-500/10 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center shadow-md border border-amber-500/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-0.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-0.5 h-1 bg-amber-400/70 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleRightPanel,\n                        className: \"absolute z-10 w-6 h-12 bg-gradient-to-r from-amber-500/20 to-amber-600/20 hover:from-amber-500/30 hover:to-amber-600/30 border border-amber-500/50 rounded-l-md transition-all duration-200 flex items-center justify-center group shadow-lg \".concat(rightCollapsed ? \"-right-0\" : \"-right-3\"),\n                        title: rightCollapsed ? \"展开右侧面板\" : \"收起右侧面板\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"12\",\n                            height: \"12\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"currentColor\",\n                            className: \"text-amber-400 transition-transform duration-200 \".concat(rightCollapsed ? \"rotate-180\" : \"rotate-0\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-b from-gray-800 to-gray-900 flex flex-col transition-all duration-300 ease-in-out \".concat(rightCollapsed ? \"w-0 overflow-hidden\" : \"\"),\n                style: {\n                    width: rightCollapsed ? \"0%\" : \"\".concat(rightWidth, \"%\"),\n                    minWidth: rightCollapsed ? \"0px\" : \"\".concat(minRightWidth, \"%\")\n                },\n                children: rightPanel\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, undefined),\n            isDragging && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 cursor-col-resize\",\n                style: {\n                    pointerEvents: \"none\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\ResizableLayout\\\\index.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResizableLayout, \"TCoEAVdsguYWSYNx4dQI7oppES4=\");\n_c = ResizableLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ResizableLayout);\nvar _c;\n$RefreshReg$(_c, \"ResizableLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ResizableLayout/index.tsx\n"));

/***/ })

});