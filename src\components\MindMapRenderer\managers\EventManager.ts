/**
 * EventManager - 事件管理器
 * 负责SimpleMindMap事件的统一管理和分发
 * 
 * 职责：
 * - 监听SimpleMindMap官方事件
 * - 事件处理和回调分发
 * - 自定义事件逻辑
 */

import { SimpleMindMapInstance, EventManagerConfig } from '../types';

export class EventManager {
  private mindMapInstance: SimpleMindMapInstance;
  private config: EventManagerConfig;
  private eventListeners: Map<string, Function> = new Map();
  private isInitialized = false;
  private isFirstRender = true;

  // 右键菜单状态管理
  private contextMenuVisible = false;
  private contextMenuPosition = { x: 0, y: 0 };
  private contextMenuNode: any = null;

  constructor(mindMapInstance: SimpleMindMapInstance, config: EventManagerConfig) {
    this.mindMapInstance = mindMapInstance;
    this.config = config;
  }

  /**
   * 初始化事件监听
   */
  initialize(): void {
    this.setupNodeEvents();
    this.setupDataEvents();
    this.setupRenderEvents();
    this.setupCanvasEvents();

    this.isInitialized = true;
    console.log('✅ 事件管理器初始化完成');
  }

  /**
   * 设置节点相关事件
   */
  private setupNodeEvents(): void {
    // 节点点击事件
    const nodeClickHandler = (node: any) => {
      console.log('🖱️ 节点点击:', node);
      this.config.onNodeClick?.(node);
    };

    // 节点激活事件
    const nodeActiveHandler = (node: any, activeNodeList: any[]) => {
      console.log('✨ 节点激活:', { node, activeNodeList });
      // 可以在这里添加节点激活的自定义逻辑
    };

    // 节点右键菜单事件
    const nodeContextMenuHandler = (event: MouseEvent, node: any) => {
      console.log('🖱️ 节点右键菜单:', { event, node });

      // 阻止默认右键菜单
      event.preventDefault();
      event.stopPropagation();

      // 计算菜单位置并显示
      const position = this.calculateMenuPosition(event);
      this.showContextMenu({ position, node });
    };

    // 绑定事件
    this.mindMapInstance.on('node_click', nodeClickHandler);
    this.mindMapInstance.on('node_active', nodeActiveHandler);
    this.mindMapInstance.on('node_contextmenu', nodeContextMenuHandler);

    // 存储事件处理器用于清理
    this.eventListeners.set('node_click', nodeClickHandler);
    this.eventListeners.set('node_active', nodeActiveHandler);
    this.eventListeners.set('node_contextmenu', nodeContextMenuHandler);
  }

  /**
   * 设置数据相关事件
   */
  private setupDataEvents(): void {
    // 数据变更事件
    const dataChangeHandler = (newData: any) => {
      console.log('📊 数据变更:', newData);
      this.config.onDataChange?.(newData);
    };

    // 数据设置前事件
    const beforeSetDataHandler = (data: any) => {
      console.log('📋 准备设置数据:', data);
    };

    // 数据设置后事件
    const setDataHandler = (data: any) => {
      console.log('📋 数据设置完成:', data);
    };

    // 绑定事件
    this.mindMapInstance.on('data_change', dataChangeHandler);
    this.mindMapInstance.on('before_set_data', beforeSetDataHandler);
    this.mindMapInstance.on('set_data', setDataHandler);

    // 存储事件处理器
    this.eventListeners.set('data_change', dataChangeHandler);
    this.eventListeners.set('before_set_data', beforeSetDataHandler);
    this.eventListeners.set('set_data', setDataHandler);
  }

  /**
   * 设置渲染相关事件
   */
  private setupRenderEvents(): void {
    // 节点树渲染结束事件
    const nodeTreeRenderEndHandler = () => {
      console.log('🎨 节点树渲染完成');

      // 完全不在EventManager中处理视图适应，交给组件级别控制
      // 这样可以确保用户的视图位置永远不会被意外重置
      console.log('✅ 渲染完成，保持当前视图位置');
      this.config.onRenderComplete?.();
    };

    // 绑定事件
    this.mindMapInstance.on('node_tree_render_end', nodeTreeRenderEndHandler);

    // 存储事件处理器
    this.eventListeners.set('node_tree_render_end', nodeTreeRenderEndHandler);
  }

  /**
   * 设置画布相关事件
   */
  private setupCanvasEvents(): void {
    // 画布点击事件
    const drawClickHandler = () => {
      console.log('🖼️ 画布点击');
      // 隐藏右键菜单
      if (this.contextMenuVisible) {
        this.hideContextMenu();
      }
    };

    // 画布拖拽事件
    const drawDragHandler = () => {
      console.log('🖼️ 画布拖拽');
    };

    // 缩放事件
    const scaleHandler = (scale: number) => {
      console.log('🔍 缩放变化:', scale);
    };

    // 绑定事件
    this.mindMapInstance.on('draw_click', drawClickHandler);
    this.mindMapInstance.on('view_data_change', drawDragHandler);
    this.mindMapInstance.on('scale', scaleHandler);

    // 存储事件处理器
    this.eventListeners.set('draw_click', drawClickHandler);
    this.eventListeners.set('view_data_change', drawDragHandler);
    this.eventListeners.set('scale', scaleHandler);
  }

  /**
   * 手动触发事件
   */
  emit(event: string, ...args: any[]): void {
    if (!this.mindMapInstance) return;

    try {
      this.mindMapInstance.emit(event, ...args);
    } catch (error) {
      console.error(`❌ 触发事件失败 ${event}:`, error);
    }
  }

  /**
   * 添加自定义事件监听器
   */
  addEventListener(event: string, handler: Function): void {
    if (!this.mindMapInstance) return;

    try {
      this.mindMapInstance.on(event, handler);
      this.eventListeners.set(event, handler);
      console.log(`✅ 添加事件监听器: ${event}`);
    } catch (error) {
      console.error(`❌ 添加事件监听器失败 ${event}:`, error);
    }
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event: string): void {
    const handler = this.eventListeners.get(event);
    if (!handler || !this.mindMapInstance) return;

    try {
      this.mindMapInstance.off(event, handler);
      this.eventListeners.delete(event);
      console.log(`✅ 移除事件监听器: ${event}`);
    } catch (error) {
      console.error(`❌ 移除事件监听器失败 ${event}:`, error);
    }
  }

  /**
   * 获取当前激活的节点列表
   */
  getActiveNodes(): any[] {
    if (!this.mindMapInstance?.renderer) return [];
    
    try {
      return this.mindMapInstance.renderer.activeNodeList || [];
    } catch (error) {
      console.error('❌ 获取激活节点失败:', error);
      return [];
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 计算菜单位置，确保不超出视口
   */
  private calculateMenuPosition(event: MouseEvent): { x: number; y: number } {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x = event.clientX;
    let y = event.clientY;

    // 预估菜单尺寸（实际尺寸会在组件中调整）
    const estimatedMenuWidth = 200;
    const estimatedMenuHeight = 300;

    // 水平方向调整
    if (x + estimatedMenuWidth > viewportWidth) {
      x = viewportWidth - estimatedMenuWidth - 10;
    }
    if (x < 10) {
      x = 10;
    }

    // 垂直方向调整
    if (y + estimatedMenuHeight > viewportHeight) {
      y = viewportHeight - estimatedMenuHeight - 10;
    }
    if (y < 10) {
      y = 10;
    }

    return { x, y };
  }

  /**
   * 显示右键菜单
   */
  showContextMenu({ position, node }: { position: { x: number; y: number }, node: any }): void {
    this.contextMenuPosition = position;
    this.contextMenuNode = node;
    this.contextMenuVisible = true;

    // 触发菜单显示回调
    this.config.onContextMenuShow?.(position, node);

    console.log('✅ 显示右键菜单:', { position, node });
  }

  /**
   * 隐藏右键菜单
   */
  hideContextMenu(): void {
    this.contextMenuVisible = false;
    this.contextMenuPosition = { x: 0, y: 0 };
    this.contextMenuNode = null;

    // 触发菜单隐藏回调
    this.config.onContextMenuHide?.();

    console.log('✅ 隐藏右键菜单');
  }

  /**
   * 获取右键菜单状态
   */
  getContextMenuState(): {
    visible: boolean;
    position: { x: number; y: number };
    node: any;
  } {
    return {
      visible: this.contextMenuVisible,
      position: this.contextMenuPosition,
      node: this.contextMenuNode
    };
  }

  /**
   * 销毁事件管理器
   */
  destroy(): void {
    // 移除所有事件监听器
    for (const [event, handler] of this.eventListeners) {
      try {
        this.mindMapInstance.off(event, handler);
      } catch (error) {
        console.error(`❌ 移除事件监听器失败 ${event}:`, error);
      }
    }

    // 清理右键菜单状态
    this.hideContextMenu();

    this.eventListeners.clear();
    this.isInitialized = false;
    // 不重置isFirstRender，保持首次渲染状态
    console.log('✅ 事件管理器销毁完成');
  }
}