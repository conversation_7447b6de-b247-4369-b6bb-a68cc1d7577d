"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts":
/*!*******************************************************!*\
  !*** ./src/components/MindMapRenderer/types/index.ts ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_MINDMAP_CONFIG: function() { return /* binding */ DEFAULT_MINDMAP_CONFIG; },\n/* harmony export */   DEFAULT_SELECTION_CONFIG: function() { return /* binding */ DEFAULT_SELECTION_CONFIG; }\n/* harmony export */ });\n/**\r\n * TypeScript类型定义\r\n * 为SimpleMindMap组件提供完整的类型支持\r\n */ // 基础几何类型\n// 默认配置\nconst DEFAULT_SELECTION_CONFIG = {\n    longPressDelay: 500,\n    moveThreshold: 8,\n    selectionBoxStyle: {\n        strokeColor: \"#8FBC8F\",\n        strokeWidth: 2,\n        fillColor: \"#8FBC8F\",\n        fillOpacity: 0.15\n    },\n    debug: false\n};\nconst DEFAULT_MINDMAP_CONFIG = {\n    readonly: false,\n    // 移除默认主题设置，让StyleManager控制主题\n    layout: \"logicalStructure\",\n    enableFreeDrag: false,\n    enableCtrlKeyNodeSelection: true,\n    enableAutoEnterTextEditWhenKeydown: true,\n    isEndNodeTextEditOnClickOuter: true,\n    maxHistoryCount: 100,\n    maxNodeCacheCount: 200,\n    fitPadding: 50,\n    mouseScaleCenterUseMousePosition: true\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\n"));

/***/ })

});