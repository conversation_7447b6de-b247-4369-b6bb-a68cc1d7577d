"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleManager: function() { return /* binding */ styleManager; }\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/components/MindMapRenderer/utils/index.ts\");\n/**\r\n * StyleManager - SimpleMindMap 样式管理器\r\n * 负责主题管理、深色主题配置和自定义样式应用\r\n */ \nclass StyleManagerImpl {\n    /**\r\n   * 初始化样式管理器\r\n   */ initialize(mindMap) {\n        try {\n            this.mindMap = mindMap;\n            // 应用默认深色主题\n            this.applyTheme(this.currentTheme);\n            // 注入自定义CSS样式\n            this.injectCustomStyles();\n            this.isApplied = true;\n            console.log(\"StyleManager initialized successfully\");\n        } catch (error) {\n            const mindMapError = new MindMapError(MindMapErrorType.THEME_APPLICATION_FAILED, \"Failed to initialize style manager: \".concat(error instanceof Error ? error.message : \"Unknown error\"), error instanceof Error ? error : undefined);\n            console.error(\"Failed to initialize style manager:\", mindMapError);\n            throw mindMapError;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ applyTheme(theme) {\n        if (!this.mindMap) {\n            console.warn(\"SimpleMindMap instance is not available\");\n            return;\n        }\n        const applyThemeInternal = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(()=>{\n            this.currentTheme = theme;\n            if (theme === \"dark\") {\n                const darkThemeConfig = this.getDarkThemeConfig();\n                this.mindMap.setTheme(\"default\"); // 先设置基础主题\n                // 应用深色主题配置\n                this.applyThemeConfig(darkThemeConfig);\n            } else {\n                // 应用其他主题\n                this.mindMap.setTheme(theme);\n            }\n            console.log('Theme \"'.concat(theme, '\" applied successfully'));\n        }, undefined, \"theme application\");\n        applyThemeInternal();\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#7c3aed\",\n                borderColor: \"#8b5cf6\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 12,\n                fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif',\n                fontWeight: 400,\n                color: \"#ffffff\",\n                padding: [\n                    4,\n                    8\n                ],\n                margin: [\n                    2,\n                    2,\n                    2,\n                    2\n                ],\n                opacity: 0.9\n            },\n            // 展开按钮样式\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#4a90e2\",\n                fillColor: \"#1a1a2e\",\n                strokeColor: \"#4a90e2\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式\n            associativeLine: {\n                strokeColor: \"#10b981\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式\n            activeNodeStyle: {\n                strokeColor: \"#fbbf24\",\n                strokeWidth: 3,\n                fillColor: \"rgba(251, 191, 36, 0.1)\"\n            },\n            // 悬停状态样式\n            hoverNodeStyle: {\n                fillColor: \"rgba(59, 130, 246, 0.1)\",\n                strokeColor: \"#60a5fa\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 应用主题配置\r\n   */ applyThemeConfig(config) {\n        if (!this.mindMap) return;\n        try {\n            // 更新思维导图的主题配置\n            Object.keys(config).forEach((key)=>{\n                if (key !== \"root\" && key !== \"second\" && key !== \"node\" && key !== \"generalization\") {\n                    // 应用全局样式\n                    if (this.mindMap.themeConfig) {\n                        this.mindMap.themeConfig[key] = config[key];\n                    }\n                }\n            });\n            // 应用节点层级样式\n            if (this.mindMap.themeConfig) {\n                this.mindMap.themeConfig.root = {\n                    ...this.mindMap.themeConfig.root,\n                    ...config.root\n                };\n                this.mindMap.themeConfig.second = {\n                    ...this.mindMap.themeConfig.second,\n                    ...config.second\n                };\n                this.mindMap.themeConfig.node = {\n                    ...this.mindMap.themeConfig.node,\n                    ...config.node\n                };\n                this.mindMap.themeConfig.generalization = {\n                    ...this.mindMap.themeConfig.generalization,\n                    ...config.generalization\n                };\n            }\n            // 触发重新渲染\n            this.mindMap.render();\n        } catch (error) {\n            console.error(\"Error applying theme config:\", error);\n        }\n    }\n    /**\r\n   * 获取指定主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 应用自定义样式\r\n   */ applyCustomStyles(styles) {\n        try {\n            // 合并自定义样式\n            this.customStyles = {\n                ...this.customStyles,\n                ...styles\n            };\n            // 验证样式值\n            const validatedStyles = this.validateStyles(this.customStyles);\n            // 应用验证后的样式\n            this.applyValidatedStyles(validatedStyles);\n            console.log(\"Custom styles applied successfully\");\n        } catch (error) {\n            console.error(\"Error applying custom styles:\", error);\n        }\n    }\n    /**\r\n   * 验证样式值\r\n   */ validateStyles(styles) {\n        const validatedStyles = {};\n        Object.keys(styles).forEach((key)=>{\n            const value = styles[key];\n            // 验证颜色值\n            if (key.includes(\"Color\") || key.includes(\"color\")) {\n                if ((0,_utils__WEBPACK_IMPORTED_MODULE_0__.isValidColor)(value)) {\n                    validatedStyles[key] = value;\n                } else {\n                    console.warn(\"Invalid color value for \".concat(key, \": \").concat(value));\n                }\n            } else if (typeof value === \"number\" && !isNaN(value)) {\n                validatedStyles[key] = value;\n            } else if (typeof value === \"string\" && value.length > 0) {\n                validatedStyles[key] = value;\n            } else if (typeof value === \"object\" && value !== null) {\n                validatedStyles[key] = value;\n            }\n        });\n        return validatedStyles;\n    }\n    /**\r\n   * 应用验证后的样式\r\n   */ applyValidatedStyles(styles) {\n        if (!this.mindMap || !this.mindMap.themeConfig) return;\n        // 递归更新主题配置\n        const updateConfig = (target, source)=>{\n            Object.keys(source).forEach((key)=>{\n                if (typeof source[key] === \"object\" && source[key] !== null && !Array.isArray(source[key])) {\n                    if (!target[key]) target[key] = {};\n                    updateConfig(target[key], source[key]);\n                } else {\n                    target[key] = source[key];\n                }\n            });\n        };\n        updateConfig(this.mindMap.themeConfig, styles);\n        // 触发重新渲染\n        this.mindMap.render();\n    }\n    /**\r\n   * 注入自定义CSS样式\r\n   */ injectCustomStyles() {\n        const styleId = \"mind-map-custom-styles\";\n        // 检查是否已存在\n        if (document.getElementById(styleId)) {\n            return;\n        }\n        const style = document.createElement(\"style\");\n        style.id = styleId;\n        style.textContent = '\\n      /* MindMapRenderer 自定义样式 */\\n      .mind-map-container {\\n        background: #1a1a2e;\\n        border-radius: 8px;\\n        overflow: hidden;\\n      }\\n      \\n      .mind-map-container svg {\\n        background: #1a1a2e;\\n      }\\n      \\n      /* 选中节点样式 */\\n      .mind-map-selected {\\n        filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));\\n      }\\n      \\n      /* 悬停效果 */\\n      .mind-map-node:hover {\\n        transition: all 0.2s ease-in-out;\\n      }\\n      \\n      /* 连线动画 */\\n      .mind-map-line {\\n        transition: stroke-width 0.2s ease-in-out;\\n      }\\n      \\n      .mind-map-line:hover {\\n        stroke-width: 3px !important;\\n      }\\n      \\n      /* 展开按钮样式 */\\n      .mind-map-expand-btn {\\n        cursor: pointer;\\n        transition: all 0.2s ease-in-out;\\n      }\\n      \\n      .mind-map-expand-btn:hover {\\n        transform: scale(1.1);\\n        filter: brightness(1.2);\\n      }\\n      \\n      /* 文本编辑样式 */\\n      .mind-map-text-edit {\\n        background: #0f172a;\\n        color: #cbd5e1;\\n        border: 2px solid #4a90e2;\\n        border-radius: 4px;\\n        font-family: \"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\\n        outline: none;\\n      }\\n      \\n      .mind-map-text-edit:focus {\\n        border-color: #60a5fa;\\n        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n      }\\n      \\n      /* 滚动条样式 */\\n      .mind-map-container ::-webkit-scrollbar {\\n        width: 8px;\\n        height: 8px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-track {\\n        background: rgba(255, 255, 255, 0.1);\\n        border-radius: 4px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-thumb {\\n        background: rgba(255, 255, 255, 0.3);\\n        border-radius: 4px;\\n      }\\n      \\n      .mind-map-container ::-webkit-scrollbar-thumb:hover {\\n        background: rgba(255, 255, 255, 0.5);\\n      }\\n      \\n      /* 加载动画 */\\n      .mind-map-loading {\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        height: 100%;\\n        background: #1a1a2e;\\n        color: #cbd5e1;\\n        font-family: \"Microsoft YaHei\", \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\\n      }\\n      \\n      .mind-map-loading::after {\\n        content: \\'\\';\\n        width: 32px;\\n        height: 32px;\\n        margin-left: 8px;\\n        border: 3px solid rgba(59, 130, 246, 0.3);\\n        border-top-color: #4a90e2;\\n        border-radius: 50%;\\n        animation: mind-map-spin 1s linear infinite;\\n      }\\n      \\n      @keyframes mind-map-spin {\\n        to {\\n          transform: rotate(360deg);\\n        }\\n      }\\n    ';\n        document.head.appendChild(style);\n        console.log(\"Custom styles injected\");\n    }\n    /**\r\n   * 重置样式\r\n   */ resetStyles() {\n        try {\n            this.customStyles = {};\n            // 重新应用当前主题\n            this.applyTheme(this.currentTheme);\n            console.log(\"Styles reset successfully\");\n        } catch (error) {\n            console.error(\"Error resetting styles:\", error);\n        }\n    }\n    /**\r\n   * 更新节点样式\r\n   */ updateNodeStyle(nodeLevel, styles) {\n        try {\n            if (!this.mindMap || !this.mindMap.themeConfig) return;\n            const validatedStyles = this.validateStyles(styles);\n            this.mindMap.themeConfig[nodeLevel] = {\n                ...this.mindMap.themeConfig[nodeLevel],\n                ...validatedStyles\n            };\n            this.mindMap.render();\n            console.log(\"\".concat(nodeLevel, \" node style updated\"));\n        } catch (error) {\n            console.error(\"Error updating \".concat(nodeLevel, \" node style:\"), error);\n        }\n    }\n    /**\r\n   * 获取当前样式配置\r\n   */ getCurrentStyles() {\n        var _this_mindMap;\n        return {\n            theme: this.currentTheme,\n            customStyles: {\n                ...this.customStyles\n            },\n            themeConfig: ((_this_mindMap = this.mindMap) === null || _this_mindMap === void 0 ? void 0 : _this_mindMap.themeConfig) || {}\n        };\n    }\n    /**\r\n   * 销毁样式管理器\r\n   */ destroy() {\n        try {\n            // 移除注入的样式\n            const styleElement = document.getElementById(\"mind-map-custom-styles\");\n            if (styleElement) {\n                styleElement.remove();\n            }\n            // 重置状态\n            this.mindMap = null;\n            this.customStyles = {};\n            this.isApplied = false;\n            console.log(\"StyleManager destroyed\");\n        } catch (error) {\n            console.error(\"Error destroying style manager:\", error);\n        }\n    }\n    constructor(){\n        this.currentTheme = \"dark\";\n        this.customStyles = {};\n        this.mindMap = null;\n        this.isApplied = false;\n    }\n}\n// 导出单例实例\nconst styleManager = new StyleManagerImpl();\n/* harmony default export */ __webpack_exports__[\"default\"] = (styleManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9TdHlsZU1hbmFnZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FRb0Q7QUFFckQsTUFBTUU7SUFPSjs7R0FFQyxHQUNELFdBQWtCRSxPQUE4QixFQUFRO1FBQ3RELElBQUk7WUFDRixJQUFJLENBQUNBLE9BQU8sR0FBR0E7WUFFZixXQUFXO1lBQ1gsSUFBSSxDQUFDQyxVQUFVLENBQUMsSUFBSSxDQUFDQyxZQUFZO1lBRWpDLGFBQWE7WUFDYixJQUFJLENBQUNDLGtCQUFrQjtZQUV2QixJQUFJLENBQUNDLFNBQVMsR0FBRztZQUNqQkMsUUFBUUMsR0FBRyxDQUFDO1FBRWQsRUFBRSxPQUFPQyxPQUFPO1lBQ2QsTUFBTUMsZUFBZSxJQUFJQyxhQUN2QkMsaUJBQWlCQyx3QkFBd0IsRUFDekMsdUNBQWdHLE9BQXpESixpQkFBaUJLLFFBQVFMLE1BQU1NLE9BQU8sR0FBRyxrQkFDaEZOLGlCQUFpQkssUUFBUUwsUUFBUU87WUFHbkNULFFBQVFFLEtBQUssQ0FBQyx1Q0FBdUNDO1lBQ3JELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsV0FBa0JPLEtBQWEsRUFBUTtRQUNyQyxJQUFJLENBQUMsSUFBSSxDQUFDZixPQUFPLEVBQUU7WUFDakJLLFFBQVFXLElBQUksQ0FBQztZQUNiO1FBQ0Y7UUFFQSxNQUFNQyxxQkFBcUJwQixtREFBV0EsQ0FDcEM7WUFDRSxJQUFJLENBQUNLLFlBQVksR0FBR2E7WUFFcEIsSUFBSUEsVUFBVSxRQUFRO2dCQUNwQixNQUFNRyxrQkFBa0IsSUFBSSxDQUFDQyxrQkFBa0I7Z0JBQy9DLElBQUksQ0FBQ25CLE9BQU8sQ0FBRW9CLFFBQVEsQ0FBQyxZQUFZLFVBQVU7Z0JBRTdDLFdBQVc7Z0JBQ1gsSUFBSSxDQUFDQyxnQkFBZ0IsQ0FBQ0g7WUFDeEIsT0FBTztnQkFDTCxTQUFTO2dCQUNULElBQUksQ0FBQ2xCLE9BQU8sQ0FBRW9CLFFBQVEsQ0FBQ0w7WUFDekI7WUFFQVYsUUFBUUMsR0FBRyxDQUFDLFVBQWdCLE9BQU5TLE9BQU07UUFDOUIsR0FDQUQsV0FDQTtRQUdGRztJQUNGO0lBRUE7O0dBRUMsR0FDRCxxQkFBaUM7UUFDL0IsT0FBTztZQUNMLGFBQWE7WUFDYkssaUJBQWlCO1lBQ2pCQyxpQkFBaUI7WUFFakIsWUFBWTtZQUNaQyxXQUFXO1lBQ1hDLFdBQVc7WUFDWEMsV0FBVztZQUNYQyxlQUFlO1lBQ2ZDLGFBQWE7WUFFYixlQUFlO1lBQ2ZDLE1BQU07Z0JBQ0pDLE9BQU87Z0JBQ1BDLFdBQVc7Z0JBQ1hDLGFBQWE7Z0JBQ2JDLGFBQWE7Z0JBQ2JDLGNBQWM7Z0JBQ2RDLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLFlBQVk7Z0JBQ1pDLE9BQU87Z0JBQ1BDLFNBQVM7b0JBQUM7b0JBQUk7aUJBQUc7Z0JBQ2pCQyxRQUFRO29CQUFDO29CQUFHO29CQUFHO29CQUFHO2lCQUFFO2dCQUNwQkMsU0FBUztnQkFDVEMsZ0JBQWdCO2dCQUNoQkMsV0FBVztZQUNiO1lBRUEsZ0JBQWdCO1lBQ2hCQyxRQUFRO2dCQUNOZCxPQUFPO2dCQUNQQyxXQUFXO2dCQUNYQyxhQUFhO2dCQUNiQyxhQUFhO2dCQUNiQyxjQUFjO2dCQUNkQyxVQUFVO2dCQUNWQyxZQUFZO2dCQUNaQyxZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxTQUFTO29CQUFDO29CQUFJO2lCQUFHO2dCQUNqQkMsUUFBUTtvQkFBQztvQkFBRztvQkFBRztvQkFBRztpQkFBRTtnQkFDcEJDLFNBQVM7Z0JBQ1RDLGdCQUFnQjtnQkFDaEJDLFdBQVc7WUFDYjtZQUVBLGtCQUFrQjtZQUNsQkUsTUFBTTtnQkFDSmYsT0FBTztnQkFDUEMsV0FBVztnQkFDWEMsYUFBYTtnQkFDYkMsYUFBYTtnQkFDYkMsY0FBYztnQkFDZEMsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsWUFBWTtnQkFDWkMsT0FBTztnQkFDUEMsU0FBUztvQkFBQztvQkFBRztpQkFBRztnQkFDaEJDLFFBQVE7b0JBQUM7b0JBQUc7b0JBQUc7b0JBQUc7aUJBQUU7Z0JBQ3BCQyxTQUFTO2dCQUNUQyxnQkFBZ0I7Z0JBQ2hCQyxXQUFXO1lBQ2I7WUFFQSxTQUFTO1lBQ1RHLGdCQUFnQjtnQkFDZGhCLE9BQU87Z0JBQ1BDLFdBQVc7Z0JBQ1hDLGFBQWE7Z0JBQ2JDLGFBQWE7Z0JBQ2JDLGNBQWM7Z0JBQ2RDLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLFlBQVk7Z0JBQ1pDLE9BQU87Z0JBQ1BDLFNBQVM7b0JBQUM7b0JBQUc7aUJBQUU7Z0JBQ2ZDLFFBQVE7b0JBQUM7b0JBQUc7b0JBQUc7b0JBQUc7aUJBQUU7Z0JBQ3BCQyxTQUFTO1lBQ1g7WUFFQSxTQUFTO1lBQ1RNLGVBQWU7WUFDZkMsZ0JBQWdCO2dCQUNkVixPQUFPO2dCQUNQUCxXQUFXO2dCQUNYa0IsYUFBYTtnQkFDYkMsYUFBYTtnQkFDYkMsUUFBUTtZQUNWO1lBRUEsUUFBUTtZQUNSQyxpQkFBaUI7Z0JBQ2ZILGFBQWE7Z0JBQ2JDLGFBQWE7Z0JBQ2JHLGlCQUFpQjtZQUNuQjtZQUVBLFNBQVM7WUFDVEMsaUJBQWlCO2dCQUNmTCxhQUFhO2dCQUNiQyxhQUFhO2dCQUNibkIsV0FBVztZQUNiO1lBRUEsU0FBUztZQUNUd0IsZ0JBQWdCO2dCQUNkeEIsV0FBVztnQkFDWGtCLGFBQWE7Z0JBQ2JDLGFBQWE7WUFDZjtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGlCQUF5Qk0sTUFBVyxFQUFRO1FBQzFDLElBQUksQ0FBQyxJQUFJLENBQUN4RCxPQUFPLEVBQUU7UUFFbkIsSUFBSTtZQUNGLGNBQWM7WUFDZHlELE9BQU9DLElBQUksQ0FBQ0YsUUFBUUcsT0FBTyxDQUFDQyxDQUFBQTtnQkFDMUIsSUFBSUEsUUFBUSxVQUFVQSxRQUFRLFlBQVlBLFFBQVEsVUFBVUEsUUFBUSxrQkFBa0I7b0JBQ3BGLFNBQVM7b0JBQ1QsSUFBSSxJQUFJLENBQUM1RCxPQUFPLENBQUU2RCxXQUFXLEVBQUU7d0JBQzdCLElBQUksQ0FBQzdELE9BQU8sQ0FBRTZELFdBQVcsQ0FBQ0QsSUFBSSxHQUFHSixNQUFNLENBQUNJLElBQUk7b0JBQzlDO2dCQUNGO1lBQ0Y7WUFFQSxXQUFXO1lBQ1gsSUFBSSxJQUFJLENBQUM1RCxPQUFPLENBQUM2RCxXQUFXLEVBQUU7Z0JBQzVCLElBQUksQ0FBQzdELE9BQU8sQ0FBQzZELFdBQVcsQ0FBQ2hDLElBQUksR0FBRztvQkFBRSxHQUFHLElBQUksQ0FBQzdCLE9BQU8sQ0FBQzZELFdBQVcsQ0FBQ2hDLElBQUk7b0JBQUUsR0FBRzJCLE9BQU8zQixJQUFJO2dCQUFDO2dCQUNuRixJQUFJLENBQUM3QixPQUFPLENBQUM2RCxXQUFXLENBQUNqQixNQUFNLEdBQUc7b0JBQUUsR0FBRyxJQUFJLENBQUM1QyxPQUFPLENBQUM2RCxXQUFXLENBQUNqQixNQUFNO29CQUFFLEdBQUdZLE9BQU9aLE1BQU07Z0JBQUM7Z0JBQ3pGLElBQUksQ0FBQzVDLE9BQU8sQ0FBQzZELFdBQVcsQ0FBQ2hCLElBQUksR0FBRztvQkFBRSxHQUFHLElBQUksQ0FBQzdDLE9BQU8sQ0FBQzZELFdBQVcsQ0FBQ2hCLElBQUk7b0JBQUUsR0FBR1csT0FBT1gsSUFBSTtnQkFBQztnQkFDbkYsSUFBSSxDQUFDN0MsT0FBTyxDQUFDNkQsV0FBVyxDQUFDZixjQUFjLEdBQUc7b0JBQUUsR0FBRyxJQUFJLENBQUM5QyxPQUFPLENBQUM2RCxXQUFXLENBQUNmLGNBQWM7b0JBQUUsR0FBR1UsT0FBT1YsY0FBYztnQkFBQztZQUNuSDtZQUVBLFNBQVM7WUFDVCxJQUFJLENBQUM5QyxPQUFPLENBQUM4RCxNQUFNO1FBRXJCLEVBQUUsT0FBT3ZELE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDaEQ7SUFDRjtJQUVBOztHQUVDLEdBQ0QsZUFBc0JRLEtBQWEsRUFBTztRQUN4QyxPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTyxJQUFJLENBQUNJLGtCQUFrQjtZQUNoQyxLQUFLO2dCQUNILE9BQU8sSUFBSSxDQUFDNkMsbUJBQW1CO1lBQ2pDO2dCQUNFLE9BQU8sSUFBSSxDQUFDN0Msa0JBQWtCO1FBQ2xDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHNCQUFtQztRQUNqQyxPQUFPO1lBQ0xHLGlCQUFpQjtZQUNqQkMsaUJBQWlCO1lBQ2pCQyxXQUFXO1lBQ1hDLFdBQVc7WUFFWEksTUFBTTtnQkFDSkMsT0FBTztnQkFDUEMsV0FBVztnQkFDWEMsYUFBYTtnQkFDYkMsYUFBYTtnQkFDYkMsY0FBYztnQkFDZEMsVUFBVTtnQkFDVkUsWUFBWTtnQkFDWkMsT0FBTztnQkFDUEMsU0FBUztvQkFBQztvQkFBSTtpQkFBRztZQUNuQjtZQUVBSyxRQUFRO2dCQUNOZCxPQUFPO2dCQUNQQyxXQUFXO2dCQUNYQyxhQUFhO2dCQUNiQyxhQUFhO2dCQUNiQyxjQUFjO2dCQUNkQyxVQUFVO2dCQUNWRSxZQUFZO2dCQUNaQyxPQUFPO2dCQUNQQyxTQUFTO29CQUFDO29CQUFHO2lCQUFHO1lBQ2xCO1lBRUFNLE1BQU07Z0JBQ0pmLE9BQU87Z0JBQ1BDLFdBQVc7Z0JBQ1hDLGFBQWE7Z0JBQ2JDLGFBQWE7Z0JBQ2JDLGNBQWM7Z0JBQ2RDLFVBQVU7Z0JBQ1ZFLFlBQVk7Z0JBQ1pDLE9BQU87Z0JBQ1BDLFNBQVM7b0JBQUM7b0JBQUc7aUJBQUc7WUFDbEI7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxrQkFBeUIyQixNQUEyQixFQUFRO1FBQzFELElBQUk7WUFDRixVQUFVO1lBQ1YsSUFBSSxDQUFDQyxZQUFZLEdBQUc7Z0JBQUUsR0FBRyxJQUFJLENBQUNBLFlBQVk7Z0JBQUUsR0FBR0QsTUFBTTtZQUFDO1lBRXRELFFBQVE7WUFDUixNQUFNRSxrQkFBa0IsSUFBSSxDQUFDQyxjQUFjLENBQUMsSUFBSSxDQUFDRixZQUFZO1lBRTdELFdBQVc7WUFDWCxJQUFJLENBQUNHLG9CQUFvQixDQUFDRjtZQUUxQi9ELFFBQVFDLEdBQUcsQ0FBQztRQUVkLEVBQUUsT0FBT0MsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsaUNBQWlDQTtRQUNqRDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxlQUF1QjJELE1BQTJCLEVBQXVCO1FBQ3ZFLE1BQU1FLGtCQUF1QyxDQUFDO1FBRTlDWCxPQUFPQyxJQUFJLENBQUNRLFFBQVFQLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDMUIsTUFBTVcsUUFBUUwsTUFBTSxDQUFDTixJQUFJO1lBRXpCLFFBQVE7WUFDUixJQUFJQSxJQUFJWSxRQUFRLENBQUMsWUFBWVosSUFBSVksUUFBUSxDQUFDLFVBQVU7Z0JBQ2xELElBQUk1RSxvREFBWUEsQ0FBQzJFLFFBQVE7b0JBQ3ZCSCxlQUFlLENBQUNSLElBQUksR0FBR1c7Z0JBQ3pCLE9BQU87b0JBQ0xsRSxRQUFRVyxJQUFJLENBQUMsMkJBQW1DdUQsT0FBUlgsS0FBSSxNQUFVLE9BQU5XO2dCQUNsRDtZQUNGLE9BRUssSUFBSSxPQUFPQSxVQUFVLFlBQVksQ0FBQ0UsTUFBTUYsUUFBUTtnQkFDbkRILGVBQWUsQ0FBQ1IsSUFBSSxHQUFHVztZQUN6QixPQUVLLElBQUksT0FBT0EsVUFBVSxZQUFZQSxNQUFNRyxNQUFNLEdBQUcsR0FBRztnQkFDdEROLGVBQWUsQ0FBQ1IsSUFBSSxHQUFHVztZQUN6QixPQUVLLElBQUksT0FBT0EsVUFBVSxZQUFZQSxVQUFVLE1BQU07Z0JBQ3BESCxlQUFlLENBQUNSLElBQUksR0FBR1c7WUFDekI7UUFDRjtRQUVBLE9BQU9IO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELHFCQUE2QkYsTUFBMkIsRUFBUTtRQUM5RCxJQUFJLENBQUMsSUFBSSxDQUFDbEUsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFDQSxPQUFPLENBQUM2RCxXQUFXLEVBQUU7UUFFaEQsV0FBVztRQUNYLE1BQU1jLGVBQWUsQ0FBQ0MsUUFBYUM7WUFDakNwQixPQUFPQyxJQUFJLENBQUNtQixRQUFRbEIsT0FBTyxDQUFDQyxDQUFBQTtnQkFDMUIsSUFBSSxPQUFPaUIsTUFBTSxDQUFDakIsSUFBSSxLQUFLLFlBQVlpQixNQUFNLENBQUNqQixJQUFJLEtBQUssUUFBUSxDQUFDa0IsTUFBTUMsT0FBTyxDQUFDRixNQUFNLENBQUNqQixJQUFJLEdBQUc7b0JBQzFGLElBQUksQ0FBQ2dCLE1BQU0sQ0FBQ2hCLElBQUksRUFBRWdCLE1BQU0sQ0FBQ2hCLElBQUksR0FBRyxDQUFDO29CQUNqQ2UsYUFBYUMsTUFBTSxDQUFDaEIsSUFBSSxFQUFFaUIsTUFBTSxDQUFDakIsSUFBSTtnQkFDdkMsT0FBTztvQkFDTGdCLE1BQU0sQ0FBQ2hCLElBQUksR0FBR2lCLE1BQU0sQ0FBQ2pCLElBQUk7Z0JBQzNCO1lBQ0Y7UUFDRjtRQUVBZSxhQUFhLElBQUksQ0FBQzNFLE9BQU8sQ0FBQzZELFdBQVcsRUFBRUs7UUFFdkMsU0FBUztRQUNULElBQUksQ0FBQ2xFLE9BQU8sQ0FBQzhELE1BQU07SUFDckI7SUFFQTs7R0FFQyxHQUNELHFCQUFtQztRQUNqQyxNQUFNa0IsVUFBVTtRQUVoQixVQUFVO1FBQ1YsSUFBSUMsU0FBU0MsY0FBYyxDQUFDRixVQUFVO1lBQ3BDO1FBQ0Y7UUFFQSxNQUFNRyxRQUFRRixTQUFTRyxhQUFhLENBQUM7UUFDckNELE1BQU1FLEVBQUUsR0FBR0w7UUFDWEcsTUFBTUcsV0FBVyxHQUFJO1FBMEdyQkwsU0FBU00sSUFBSSxDQUFDQyxXQUFXLENBQUNMO1FBQzFCOUUsUUFBUUMsR0FBRyxDQUFDO0lBQ2Q7SUFFQTs7R0FFQyxHQUNELGNBQTJCO1FBQ3pCLElBQUk7WUFDRixJQUFJLENBQUM2RCxZQUFZLEdBQUcsQ0FBQztZQUVyQixXQUFXO1lBQ1gsSUFBSSxDQUFDbEUsVUFBVSxDQUFDLElBQUksQ0FBQ0MsWUFBWTtZQUVqQ0csUUFBUUMsR0FBRyxDQUFDO1FBRWQsRUFBRSxPQUFPQyxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQywyQkFBMkJBO1FBQzNDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGdCQUF1Qm9GLFNBQXdELEVBQUV6QixNQUEyQixFQUFRO1FBQ2xILElBQUk7WUFDRixJQUFJLENBQUMsSUFBSSxDQUFDbEUsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFDQSxPQUFPLENBQUM2RCxXQUFXLEVBQUU7WUFFaEQsTUFBTU8sa0JBQWtCLElBQUksQ0FBQ0MsY0FBYyxDQUFDSDtZQUM1QyxJQUFJLENBQUNsRSxPQUFPLENBQUM2RCxXQUFXLENBQUM4QixVQUFVLEdBQUc7Z0JBQ3BDLEdBQUcsSUFBSSxDQUFDM0YsT0FBTyxDQUFDNkQsV0FBVyxDQUFDOEIsVUFBVTtnQkFDdEMsR0FBR3ZCLGVBQWU7WUFDcEI7WUFFQSxJQUFJLENBQUNwRSxPQUFPLENBQUM4RCxNQUFNO1lBQ25CekQsUUFBUUMsR0FBRyxDQUFDLEdBQWEsT0FBVnFGLFdBQVU7UUFFM0IsRUFBRSxPQUFPcEYsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsa0JBQTRCLE9BQVZvRixXQUFVLGlCQUFlcEY7UUFDM0Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsbUJBQStDO1lBSTlCO1FBSGYsT0FBTztZQUNMUSxPQUFPLElBQUksQ0FBQ2IsWUFBWTtZQUN4QmlFLGNBQWM7Z0JBQUUsR0FBRyxJQUFJLENBQUNBLFlBQVk7WUFBQztZQUNyQ04sYUFBYSxzQkFBSSxDQUFDN0QsT0FBTyxjQUFaLGtEQUFjNkQsV0FBVyxLQUFJLENBQUM7UUFDN0M7SUFDRjtJQUVBOztHQUVDLEdBQ0QsVUFBdUI7UUFDckIsSUFBSTtZQUNGLFVBQVU7WUFDVixNQUFNaUMsZUFBZWIsU0FBU0MsY0FBYyxDQUFDO1lBQzdDLElBQUlZLGNBQWM7Z0JBQ2hCQSxhQUFhQyxNQUFNO1lBQ3JCO1lBRUEsT0FBTztZQUNQLElBQUksQ0FBQy9GLE9BQU8sR0FBRztZQUNmLElBQUksQ0FBQ21FLFlBQVksR0FBRyxDQUFDO1lBQ3JCLElBQUksQ0FBQy9ELFNBQVMsR0FBRztZQUVqQkMsUUFBUUMsR0FBRyxDQUFDO1FBRWQsRUFBRSxPQUFPQyxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ25EO0lBQ0Y7O2FBemlCT0wsZUFBZTthQUNmaUUsZUFBb0MsQ0FBQzthQUVwQ25FLFVBQXdDO2FBQ3hDSSxZQUFZOztBQXNpQnRCO0FBRUEsU0FBUztBQUNGLE1BQU00RixlQUFlLElBQUlsRyxtQkFBbUI7QUFFbkQsK0RBQWVrRyxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9TdHlsZU1hbmFnZXIudHM/OWIwYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogU3R5bGVNYW5hZ2VyIC0gU2ltcGxlTWluZE1hcCDmoLflvI/nrqHnkIblmahcclxuICog6LSf6LSj5Li76aKY566h55CG44CB5rex6Imy5Li76aKY6YWN572u5ZKM6Ieq5a6a5LmJ5qC35byP5bqU55SoXHJcbiAqL1xyXG5cclxuaW1wb3J0IHR5cGUgeyBcclxuICBTdHlsZU1hbmFnZXIsIFxyXG4gIFNpbXBsZU1pbmRNYXBJbnN0YW5jZSxcclxuICBNaW5kTWFwRXJyb3IsXHJcbiAgTWluZE1hcEVycm9yVHlwZVxyXG59IGZyb20gJy4uL3R5cGVzJztcclxuaW1wb3J0IHsgaXNWYWxpZENvbG9yLCBzYWZlRXhlY3V0ZSB9IGZyb20gJy4uL3V0aWxzJztcclxuXHJcbmNsYXNzIFN0eWxlTWFuYWdlckltcGwgaW1wbGVtZW50cyBTdHlsZU1hbmFnZXIge1xyXG4gIHB1YmxpYyBjdXJyZW50VGhlbWUgPSAnZGFyayc7XHJcbiAgcHVibGljIGN1c3RvbVN0eWxlczogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9O1xyXG4gIFxyXG4gIHByaXZhdGUgbWluZE1hcDogU2ltcGxlTWluZE1hcEluc3RhbmNlIHwgbnVsbCA9IG51bGw7XHJcbiAgcHJpdmF0ZSBpc0FwcGxpZWQgPSBmYWxzZTtcclxuXHJcbiAgLyoqXHJcbiAgICog5Yid5aeL5YyW5qC35byP566h55CG5ZmoXHJcbiAgICovXHJcbiAgcHVibGljIGluaXRpYWxpemUobWluZE1hcDogU2ltcGxlTWluZE1hcEluc3RhbmNlKTogdm9pZCB7XHJcbiAgICB0cnkge1xyXG4gICAgICB0aGlzLm1pbmRNYXAgPSBtaW5kTWFwO1xyXG4gICAgICBcclxuICAgICAgLy8g5bqU55So6buY6K6k5rex6Imy5Li76aKYXHJcbiAgICAgIHRoaXMuYXBwbHlUaGVtZSh0aGlzLmN1cnJlbnRUaGVtZSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyDms6jlhaXoh6rlrprkuYlDU1PmoLflvI9cclxuICAgICAgdGhpcy5pbmplY3RDdXN0b21TdHlsZXMoKTtcclxuICAgICAgXHJcbiAgICAgIHRoaXMuaXNBcHBsaWVkID0gdHJ1ZTtcclxuICAgICAgY29uc29sZS5sb2coJ1N0eWxlTWFuYWdlciBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHknKTtcclxuICAgICAgXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zdCBtaW5kTWFwRXJyb3IgPSBuZXcgTWluZE1hcEVycm9yKFxyXG4gICAgICAgIE1pbmRNYXBFcnJvclR5cGUuVEhFTUVfQVBQTElDQVRJT05fRkFJTEVELFxyXG4gICAgICAgIGBGYWlsZWQgdG8gaW5pdGlhbGl6ZSBzdHlsZSBtYW5hZ2VyOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWAsXHJcbiAgICAgICAgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yIDogdW5kZWZpbmVkXHJcbiAgICAgICk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSBzdHlsZSBtYW5hZ2VyOicsIG1pbmRNYXBFcnJvcik7XHJcbiAgICAgIHRocm93IG1pbmRNYXBFcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOW6lOeUqOS4u+mimFxyXG4gICAqL1xyXG4gIHB1YmxpYyBhcHBseVRoZW1lKHRoZW1lOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwKSB7XHJcbiAgICAgIGNvbnNvbGUud2FybignU2ltcGxlTWluZE1hcCBpbnN0YW5jZSBpcyBub3QgYXZhaWxhYmxlJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBhcHBseVRoZW1lSW50ZXJuYWwgPSBzYWZlRXhlY3V0ZShcclxuICAgICAgKCkgPT4ge1xyXG4gICAgICAgIHRoaXMuY3VycmVudFRoZW1lID0gdGhlbWU7XHJcbiAgICAgICAgXHJcbiAgICAgICAgaWYgKHRoZW1lID09PSAnZGFyaycpIHtcclxuICAgICAgICAgIGNvbnN0IGRhcmtUaGVtZUNvbmZpZyA9IHRoaXMuZ2V0RGFya1RoZW1lQ29uZmlnKCk7XHJcbiAgICAgICAgICB0aGlzLm1pbmRNYXAhLnNldFRoZW1lKCdkZWZhdWx0Jyk7IC8vIOWFiOiuvue9ruWfuuehgOS4u+mimFxyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyDlupTnlKjmt7HoibLkuLvpopjphY3nva5cclxuICAgICAgICAgIHRoaXMuYXBwbHlUaGVtZUNvbmZpZyhkYXJrVGhlbWVDb25maWcpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyDlupTnlKjlhbbku5bkuLvpophcclxuICAgICAgICAgIHRoaXMubWluZE1hcCEuc2V0VGhlbWUodGhlbWUpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICBjb25zb2xlLmxvZyhgVGhlbWUgXCIke3RoZW1lfVwiIGFwcGxpZWQgc3VjY2Vzc2Z1bGx5YCk7XHJcbiAgICAgIH0sXHJcbiAgICAgIHVuZGVmaW5lZCxcclxuICAgICAgJ3RoZW1lIGFwcGxpY2F0aW9uJ1xyXG4gICAgKTtcclxuXHJcbiAgICBhcHBseVRoZW1lSW50ZXJuYWwoKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOiOt+WPlua3seiJsuS4u+mimOmFjee9riAtIOm7kemHkemFjeiJslxyXG4gICAqL1xyXG4gIHB1YmxpYyBnZXREYXJrVGhlbWVDb25maWcoKTogYW55IHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIC8vIOiDjOaZr+mFjee9riAtIOa3sem7keiJslxyXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMGEwYTBhJyxcclxuICAgICAgYmFja2dyb3VuZEltYWdlOiAnJyxcclxuICAgICAgXHJcbiAgICAgIC8vIOi/nue6v+mFjee9riAtIOmHkeiJslxyXG4gICAgICBsaW5lQ29sb3I6ICcjRkZENzAwJyxcclxuICAgICAgbGluZVdpZHRoOiAyLFxyXG4gICAgICBsaW5lU3R5bGU6ICdzb2xpZCcsXHJcbiAgICAgIGxpbmVEYXNoYXJyYXk6ICdub25lJyxcclxuICAgICAgbGluZU9wYWNpdHk6IDAuOSxcclxuICAgICAgXHJcbiAgICAgIC8vIOagueiKgueCueagt+W8jyAtIOm7kemHkeS4u+mimFxyXG4gICAgICByb290OiB7XHJcbiAgICAgICAgc2hhcGU6ICdyZWN0YW5nbGUnLFxyXG4gICAgICAgIGZpbGxDb2xvcjogJyMxYTFhMWEnLFxyXG4gICAgICAgIGJvcmRlckNvbG9yOiAnI0ZGRDcwMCcsXHJcbiAgICAgICAgYm9yZGVyV2lkdGg6IDMsXHJcbiAgICAgICAgYm9yZGVyUmFkaXVzOiA4LFxyXG4gICAgICAgIGZvbnRTaXplOiAyMCxcclxuICAgICAgICBmb250RmFtaWx5OiAndmFyKC0tZm9udC1mYW1pbHktaGFuZHdyaXR0ZW4sIFwiTWljcm9zb2Z0IFlhSGVpXCIsIFwiUGluZ0ZhbmcgU0NcIiwgc2Fucy1zZXJpZiknLFxyXG4gICAgICAgIGZvbnRXZWlnaHQ6IDcwMCxcclxuICAgICAgICBjb2xvcjogJyNGRkQ3MDAnLFxyXG4gICAgICAgIHBhZGRpbmc6IFsxNSwgMjBdLFxyXG4gICAgICAgIG1hcmdpbjogWzAsIDAsIDAsIDBdLFxyXG4gICAgICAgIG9wYWNpdHk6IDEsXHJcbiAgICAgICAgdGV4dERlY29yYXRpb246ICdub25lJyxcclxuICAgICAgICBmb250U3R5bGU6ICdub3JtYWwnXHJcbiAgICAgIH0sXHJcbiAgICAgIFxyXG4gICAgICAvLyDkuoznuqfoioLngrnmoLflvI8gLSDmuJDlj5jph5HoibJcclxuICAgICAgc2Vjb25kOiB7XHJcbiAgICAgICAgc2hhcGU6ICdyZWN0YW5nbGUnLFxyXG4gICAgICAgIGZpbGxDb2xvcjogJyMyYTJhMmEnLFxyXG4gICAgICAgIGJvcmRlckNvbG9yOiAnI0ZGQTUwMCcsXHJcbiAgICAgICAgYm9yZGVyV2lkdGg6IDIsXHJcbiAgICAgICAgYm9yZGVyUmFkaXVzOiA2LFxyXG4gICAgICAgIGZvbnRTaXplOiAxNyxcclxuICAgICAgICBmb250RmFtaWx5OiAndmFyKC0tZm9udC1mYW1pbHktaGFuZHdyaXR0ZW4sIFwiTWljcm9zb2Z0IFlhSGVpXCIsIFwiUGluZ0ZhbmcgU0NcIiwgc2Fucy1zZXJpZiknLFxyXG4gICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgICBjb2xvcjogJyNGRkE1MDAnLFxyXG4gICAgICAgIHBhZGRpbmc6IFsxMCwgMTVdLFxyXG4gICAgICAgIG1hcmdpbjogWzUsIDUsIDUsIDVdLFxyXG4gICAgICAgIG9wYWNpdHk6IDEsXHJcbiAgICAgICAgdGV4dERlY29yYXRpb246ICdub25lJyxcclxuICAgICAgICBmb250U3R5bGU6ICdub3JtYWwnXHJcbiAgICAgIH0sXHJcbiAgICAgIFxyXG4gICAgICAvLyDkuInnuqflj4rku6XkuIvoioLngrnmoLflvI8gLSDmtYXph5HoibJcclxuICAgICAgbm9kZToge1xyXG4gICAgICAgIHNoYXBlOiAncmVjdGFuZ2xlJyxcclxuICAgICAgICBmaWxsQ29sb3I6ICcjMWUxZTFlJyxcclxuICAgICAgICBib3JkZXJDb2xvcjogJyNEQUE1MjAnLFxyXG4gICAgICAgIGJvcmRlcldpZHRoOiAxLFxyXG4gICAgICAgIGJvcmRlclJhZGl1czogNCxcclxuICAgICAgICBmb250U2l6ZTogMTUsXHJcbiAgICAgICAgZm9udEZhbWlseTogJ3ZhcigtLWZvbnQtZmFtaWx5LWhhbmR3cml0dGVuLCBcIk1pY3Jvc29mdCBZYUhlaVwiLCBcIlBpbmdGYW5nIFNDXCIsIHNhbnMtc2VyaWYpJyxcclxuICAgICAgICBmb250V2VpZ2h0OiA1MDAsXHJcbiAgICAgICAgY29sb3I6ICcjREFBNTIwJyxcclxuICAgICAgICBwYWRkaW5nOiBbOCwgMTJdLFxyXG4gICAgICAgIG1hcmdpbjogWzMsIDMsIDMsIDNdLFxyXG4gICAgICAgIG9wYWNpdHk6IDEsXHJcbiAgICAgICAgdGV4dERlY29yYXRpb246ICdub25lJyxcclxuICAgICAgICBmb250U3R5bGU6ICdub3JtYWwnXHJcbiAgICAgIH0sXHJcbiAgICAgIFxyXG4gICAgICAvLyDmpoLopoHoioLngrnmoLflvI9cclxuICAgICAgZ2VuZXJhbGl6YXRpb246IHtcclxuICAgICAgICBzaGFwZTogJ3JlY3RhbmdsZScsXHJcbiAgICAgICAgZmlsbENvbG9yOiAnIzdjM2FlZCcsXHJcbiAgICAgICAgYm9yZGVyQ29sb3I6ICcjOGI1Y2Y2JyxcclxuICAgICAgICBib3JkZXJXaWR0aDogMSxcclxuICAgICAgICBib3JkZXJSYWRpdXM6IDQsXHJcbiAgICAgICAgZm9udFNpemU6IDEyLFxyXG4gICAgICAgIGZvbnRGYW1pbHk6ICdcIk1pY3Jvc29mdCBZYUhlaVwiLCBcIlBpbmdGYW5nIFNDXCIsIFwiSGVsdmV0aWNhIE5ldWVcIiwgQXJpYWwsIHNhbnMtc2VyaWYnLFxyXG4gICAgICAgIGZvbnRXZWlnaHQ6IDQwMCxcclxuICAgICAgICBjb2xvcjogJyNmZmZmZmYnLFxyXG4gICAgICAgIHBhZGRpbmc6IFs0LCA4XSxcclxuICAgICAgICBtYXJnaW46IFsyLCAyLCAyLCAyXSxcclxuICAgICAgICBvcGFjaXR5OiAwLjlcclxuICAgICAgfSxcclxuICAgICAgXHJcbiAgICAgIC8vIOWxleW8gOaMiemSruagt+W8j1xyXG4gICAgICBleHBhbmRCdG5TaXplOiAxNixcclxuICAgICAgZXhwYW5kQnRuU3R5bGU6IHtcclxuICAgICAgICBjb2xvcjogJyM0YTkwZTInLFxyXG4gICAgICAgIGZpbGxDb2xvcjogJyMxYTFhMmUnLFxyXG4gICAgICAgIHN0cm9rZUNvbG9yOiAnIzRhOTBlMicsXHJcbiAgICAgICAgc3Ryb2tlV2lkdGg6IDIsXHJcbiAgICAgICAgcmFkaXVzOiA4XHJcbiAgICAgIH0sXHJcbiAgICAgIFxyXG4gICAgICAvLyDov57mjqXngrnmoLflvI9cclxuICAgICAgYXNzb2NpYXRpdmVMaW5lOiB7XHJcbiAgICAgICAgc3Ryb2tlQ29sb3I6ICcjMTBiOTgxJyxcclxuICAgICAgICBzdHJva2VXaWR0aDogMixcclxuICAgICAgICBzdHJva2VEYXNoYXJyYXk6ICc1LDUnXHJcbiAgICAgIH0sXHJcbiAgICAgIFxyXG4gICAgICAvLyDmv4DmtLvnirbmgIHmoLflvI9cclxuICAgICAgYWN0aXZlTm9kZVN0eWxlOiB7XHJcbiAgICAgICAgc3Ryb2tlQ29sb3I6ICcjZmJiZjI0JyxcclxuICAgICAgICBzdHJva2VXaWR0aDogMyxcclxuICAgICAgICBmaWxsQ29sb3I6ICdyZ2JhKDI1MSwgMTkxLCAzNiwgMC4xKSdcclxuICAgICAgfSxcclxuICAgICAgXHJcbiAgICAgIC8vIOaCrOWBnOeKtuaAgeagt+W8j1xyXG4gICAgICBob3Zlck5vZGVTdHlsZToge1xyXG4gICAgICAgIGZpbGxDb2xvcjogJ3JnYmEoNTksIDEzMCwgMjQ2LCAwLjEpJyxcclxuICAgICAgICBzdHJva2VDb2xvcjogJyM2MGE1ZmEnLFxyXG4gICAgICAgIHN0cm9rZVdpZHRoOiAyXHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDlupTnlKjkuLvpopjphY3nva5cclxuICAgKi9cclxuICBwcml2YXRlIGFwcGx5VGhlbWVDb25maWcoY29uZmlnOiBhbnkpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwKSByZXR1cm47XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5pu05paw5oCd57u05a+85Zu+55qE5Li76aKY6YWN572uXHJcbiAgICAgIE9iamVjdC5rZXlzKGNvbmZpZykuZm9yRWFjaChrZXkgPT4ge1xyXG4gICAgICAgIGlmIChrZXkgIT09ICdyb290JyAmJiBrZXkgIT09ICdzZWNvbmQnICYmIGtleSAhPT0gJ25vZGUnICYmIGtleSAhPT0gJ2dlbmVyYWxpemF0aW9uJykge1xyXG4gICAgICAgICAgLy8g5bqU55So5YWo5bGA5qC35byPXHJcbiAgICAgICAgICBpZiAodGhpcy5taW5kTWFwIS50aGVtZUNvbmZpZykge1xyXG4gICAgICAgICAgICB0aGlzLm1pbmRNYXAhLnRoZW1lQ29uZmlnW2tleV0gPSBjb25maWdba2V5XTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgLy8g5bqU55So6IqC54K55bGC57qn5qC35byPXHJcbiAgICAgIGlmICh0aGlzLm1pbmRNYXAudGhlbWVDb25maWcpIHtcclxuICAgICAgICB0aGlzLm1pbmRNYXAudGhlbWVDb25maWcucm9vdCA9IHsgLi4udGhpcy5taW5kTWFwLnRoZW1lQ29uZmlnLnJvb3QsIC4uLmNvbmZpZy5yb290IH07XHJcbiAgICAgICAgdGhpcy5taW5kTWFwLnRoZW1lQ29uZmlnLnNlY29uZCA9IHsgLi4udGhpcy5taW5kTWFwLnRoZW1lQ29uZmlnLnNlY29uZCwgLi4uY29uZmlnLnNlY29uZCB9O1xyXG4gICAgICAgIHRoaXMubWluZE1hcC50aGVtZUNvbmZpZy5ub2RlID0geyAuLi50aGlzLm1pbmRNYXAudGhlbWVDb25maWcubm9kZSwgLi4uY29uZmlnLm5vZGUgfTtcclxuICAgICAgICB0aGlzLm1pbmRNYXAudGhlbWVDb25maWcuZ2VuZXJhbGl6YXRpb24gPSB7IC4uLnRoaXMubWluZE1hcC50aGVtZUNvbmZpZy5nZW5lcmFsaXphdGlvbiwgLi4uY29uZmlnLmdlbmVyYWxpemF0aW9uIH07XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIOinpuWPkemHjeaWsOa4suafk1xyXG4gICAgICB0aGlzLm1pbmRNYXAucmVuZGVyKCk7XHJcbiAgICAgIFxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYXBwbHlpbmcgdGhlbWUgY29uZmlnOicsIGVycm9yKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOiOt+WPluaMh+WumuS4u+mimOmFjee9rlxyXG4gICAqL1xyXG4gIHB1YmxpYyBnZXRUaGVtZUNvbmZpZyh0aGVtZTogc3RyaW5nKTogYW55IHtcclxuICAgIHN3aXRjaCAodGhlbWUpIHtcclxuICAgICAgY2FzZSAnZGFyayc6XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0RGFya1RoZW1lQ29uZmlnKCk7XHJcbiAgICAgIGNhc2UgJ2xpZ2h0JzpcclxuICAgICAgICByZXR1cm4gdGhpcy5nZXRMaWdodFRoZW1lQ29uZmlnKCk7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0RGFya1RoZW1lQ29uZmlnKCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDojrflj5bmtYXoibLkuLvpopjphY3nva5cclxuICAgKi9cclxuICBwcml2YXRlIGdldExpZ2h0VGhlbWVDb25maWcoKTogYW55IHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmZmZmZmYnLFxyXG4gICAgICBiYWNrZ3JvdW5kSW1hZ2U6ICcnLFxyXG4gICAgICBsaW5lQ29sb3I6ICcjNjY2NjY2JyxcclxuICAgICAgbGluZVdpZHRoOiAxLFxyXG4gICAgICBcclxuICAgICAgcm9vdDoge1xyXG4gICAgICAgIHNoYXBlOiAncmVjdGFuZ2xlJyxcclxuICAgICAgICBmaWxsQ29sb3I6ICcjZjNmNGY2JyxcclxuICAgICAgICBib3JkZXJDb2xvcjogJyMzNzQxNTEnLFxyXG4gICAgICAgIGJvcmRlcldpZHRoOiAyLFxyXG4gICAgICAgIGJvcmRlclJhZGl1czogOCxcclxuICAgICAgICBmb250U2l6ZTogMTgsXHJcbiAgICAgICAgZm9udFdlaWdodDogNjAwLFxyXG4gICAgICAgIGNvbG9yOiAnIzExMTgyNycsXHJcbiAgICAgICAgcGFkZGluZzogWzEyLCAxNl1cclxuICAgICAgfSxcclxuICAgICAgXHJcbiAgICAgIHNlY29uZDoge1xyXG4gICAgICAgIHNoYXBlOiAncmVjdGFuZ2xlJyxcclxuICAgICAgICBmaWxsQ29sb3I6ICcjZjlmYWZiJyxcclxuICAgICAgICBib3JkZXJDb2xvcjogJyM2YjcyODAnLFxyXG4gICAgICAgIGJvcmRlcldpZHRoOiAxLFxyXG4gICAgICAgIGJvcmRlclJhZGl1czogNixcclxuICAgICAgICBmb250U2l6ZTogMTYsXHJcbiAgICAgICAgZm9udFdlaWdodDogNTAwLFxyXG4gICAgICAgIGNvbG9yOiAnIzM3NDE1MScsXHJcbiAgICAgICAgcGFkZGluZzogWzgsIDEyXVxyXG4gICAgICB9LFxyXG4gICAgICBcclxuICAgICAgbm9kZToge1xyXG4gICAgICAgIHNoYXBlOiAncmVjdGFuZ2xlJyxcclxuICAgICAgICBmaWxsQ29sb3I6ICcjZmZmZmZmJyxcclxuICAgICAgICBib3JkZXJDb2xvcjogJyNkMWQ1ZGInLFxyXG4gICAgICAgIGJvcmRlcldpZHRoOiAxLFxyXG4gICAgICAgIGJvcmRlclJhZGl1czogNCxcclxuICAgICAgICBmb250U2l6ZTogMTQsXHJcbiAgICAgICAgZm9udFdlaWdodDogNDAwLFxyXG4gICAgICAgIGNvbG9yOiAnIzRiNTU2MycsXHJcbiAgICAgICAgcGFkZGluZzogWzYsIDEwXVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5bqU55So6Ieq5a6a5LmJ5qC35byPXHJcbiAgICovXHJcbiAgcHVibGljIGFwcGx5Q3VzdG9tU3R5bGVzKHN0eWxlczogUmVjb3JkPHN0cmluZywgYW55Pik6IHZvaWQge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5ZCI5bm26Ieq5a6a5LmJ5qC35byPXHJcbiAgICAgIHRoaXMuY3VzdG9tU3R5bGVzID0geyAuLi50aGlzLmN1c3RvbVN0eWxlcywgLi4uc3R5bGVzIH07XHJcbiAgICAgIFxyXG4gICAgICAvLyDpqozor4HmoLflvI/lgLxcclxuICAgICAgY29uc3QgdmFsaWRhdGVkU3R5bGVzID0gdGhpcy52YWxpZGF0ZVN0eWxlcyh0aGlzLmN1c3RvbVN0eWxlcyk7XHJcbiAgICAgIFxyXG4gICAgICAvLyDlupTnlKjpqozor4HlkI7nmoTmoLflvI9cclxuICAgICAgdGhpcy5hcHBseVZhbGlkYXRlZFN0eWxlcyh2YWxpZGF0ZWRTdHlsZXMpO1xyXG4gICAgICBcclxuICAgICAgY29uc29sZS5sb2coJ0N1c3RvbSBzdHlsZXMgYXBwbGllZCBzdWNjZXNzZnVsbHknKTtcclxuICAgICAgXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhcHBseWluZyBjdXN0b20gc3R5bGVzOicsIGVycm9yKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOmqjOivgeagt+W8j+WAvFxyXG4gICAqL1xyXG4gIHByaXZhdGUgdmFsaWRhdGVTdHlsZXMoc3R5bGVzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICBjb25zdCB2YWxpZGF0ZWRTdHlsZXM6IFJlY29yZDxzdHJpbmcsIGFueT4gPSB7fTtcclxuICAgIFxyXG4gICAgT2JqZWN0LmtleXMoc3R5bGVzKS5mb3JFYWNoKGtleSA9PiB7XHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc3R5bGVzW2tleV07XHJcbiAgICAgIFxyXG4gICAgICAvLyDpqozor4HpopzoibLlgLxcclxuICAgICAgaWYgKGtleS5pbmNsdWRlcygnQ29sb3InKSB8fCBrZXkuaW5jbHVkZXMoJ2NvbG9yJykpIHtcclxuICAgICAgICBpZiAoaXNWYWxpZENvbG9yKHZhbHVlKSkge1xyXG4gICAgICAgICAgdmFsaWRhdGVkU3R5bGVzW2tleV0gPSB2YWx1ZTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS53YXJuKGBJbnZhbGlkIGNvbG9yIHZhbHVlIGZvciAke2tleX06ICR7dmFsdWV9YCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIC8vIOmqjOivgeaVsOWAvFxyXG4gICAgICBlbHNlIGlmICh0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInICYmICFpc05hTih2YWx1ZSkpIHtcclxuICAgICAgICB2YWxpZGF0ZWRTdHlsZXNba2V5XSA9IHZhbHVlO1xyXG4gICAgICB9XHJcbiAgICAgIC8vIOmqjOivgeWtl+espuS4slxyXG4gICAgICBlbHNlIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnICYmIHZhbHVlLmxlbmd0aCA+IDApIHtcclxuICAgICAgICB2YWxpZGF0ZWRTdHlsZXNba2V5XSA9IHZhbHVlO1xyXG4gICAgICB9XHJcbiAgICAgIC8vIOmqjOivgeWvueixoVxyXG4gICAgICBlbHNlIGlmICh0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnICYmIHZhbHVlICE9PSBudWxsKSB7XHJcbiAgICAgICAgdmFsaWRhdGVkU3R5bGVzW2tleV0gPSB2YWx1ZTtcclxuICAgICAgfVxyXG4gICAgfSk7XHJcbiAgICBcclxuICAgIHJldHVybiB2YWxpZGF0ZWRTdHlsZXM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDlupTnlKjpqozor4HlkI7nmoTmoLflvI9cclxuICAgKi9cclxuICBwcml2YXRlIGFwcGx5VmFsaWRhdGVkU3R5bGVzKHN0eWxlczogUmVjb3JkPHN0cmluZywgYW55Pik6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXAgfHwgIXRoaXMubWluZE1hcC50aGVtZUNvbmZpZykgcmV0dXJuO1xyXG4gICAgXHJcbiAgICAvLyDpgJLlvZLmm7TmlrDkuLvpopjphY3nva5cclxuICAgIGNvbnN0IHVwZGF0ZUNvbmZpZyA9ICh0YXJnZXQ6IGFueSwgc291cmNlOiBhbnkpID0+IHtcclxuICAgICAgT2JqZWN0LmtleXMoc291cmNlKS5mb3JFYWNoKGtleSA9PiB7XHJcbiAgICAgICAgaWYgKHR5cGVvZiBzb3VyY2Vba2V5XSA9PT0gJ29iamVjdCcgJiYgc291cmNlW2tleV0gIT09IG51bGwgJiYgIUFycmF5LmlzQXJyYXkoc291cmNlW2tleV0pKSB7XHJcbiAgICAgICAgICBpZiAoIXRhcmdldFtrZXldKSB0YXJnZXRba2V5XSA9IHt9O1xyXG4gICAgICAgICAgdXBkYXRlQ29uZmlnKHRhcmdldFtrZXldLCBzb3VyY2Vba2V5XSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgIH07XHJcbiAgICBcclxuICAgIHVwZGF0ZUNvbmZpZyh0aGlzLm1pbmRNYXAudGhlbWVDb25maWcsIHN0eWxlcyk7XHJcbiAgICBcclxuICAgIC8vIOinpuWPkemHjeaWsOa4suafk1xyXG4gICAgdGhpcy5taW5kTWFwLnJlbmRlcigpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5rOo5YWl6Ieq5a6a5LmJQ1NT5qC35byPXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBpbmplY3RDdXN0b21TdHlsZXMoKTogdm9pZCB7XHJcbiAgICBjb25zdCBzdHlsZUlkID0gJ21pbmQtbWFwLWN1c3RvbS1zdHlsZXMnO1xyXG4gICAgXHJcbiAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKhcclxuICAgIGlmIChkb2N1bWVudC5nZXRFbGVtZW50QnlJZChzdHlsZUlkKSkge1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnN0IHN0eWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnc3R5bGUnKTtcclxuICAgIHN0eWxlLmlkID0gc3R5bGVJZDtcclxuICAgIHN0eWxlLnRleHRDb250ZW50ID0gYFxyXG4gICAgICAvKiBNaW5kTWFwUmVuZGVyZXIg6Ieq5a6a5LmJ5qC35byPICovXHJcbiAgICAgIC5taW5kLW1hcC1jb250YWluZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQ6ICMxYTFhMmU7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC5taW5kLW1hcC1jb250YWluZXIgc3ZnIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjMWExYTJlO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvKiDpgInkuK3oioLngrnmoLflvI8gKi9cclxuICAgICAgLm1pbmQtbWFwLXNlbGVjdGVkIHtcclxuICAgICAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMCA4cHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuNikpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvKiDmgqzlgZzmlYjmnpwgKi9cclxuICAgICAgLm1pbmQtbWFwLW5vZGU6aG92ZXIge1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW4tb3V0O1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvKiDov57nur/liqjnlLsgKi9cclxuICAgICAgLm1pbmQtbWFwLWxpbmUge1xyXG4gICAgICAgIHRyYW5zaXRpb246IHN0cm9rZS13aWR0aCAwLjJzIGVhc2UtaW4tb3V0O1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAubWluZC1tYXAtbGluZTpob3ZlciB7XHJcbiAgICAgICAgc3Ryb2tlLXdpZHRoOiAzcHggIWltcG9ydGFudDtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLyog5bGV5byA5oyJ6ZKu5qC35byPICovXHJcbiAgICAgIC5taW5kLW1hcC1leHBhbmQtYnRuIHtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC5taW5kLW1hcC1leHBhbmQtYnRuOmhvdmVyIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgICAgZmlsdGVyOiBicmlnaHRuZXNzKDEuMik7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8qIOaWh+acrOe8lui+keagt+W8jyAqL1xyXG4gICAgICAubWluZC1tYXAtdGV4dC1lZGl0IHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjMGYxNzJhO1xyXG4gICAgICAgIGNvbG9yOiAjY2JkNWUxO1xyXG4gICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICM0YTkwZTI7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gICAgICAgIGZvbnQtZmFtaWx5OiBcIk1pY3Jvc29mdCBZYUhlaVwiLCBcIlBpbmdGYW5nIFNDXCIsIFwiSGVsdmV0aWNhIE5ldWVcIiwgQXJpYWwsIHNhbnMtc2VyaWY7XHJcbiAgICAgICAgb3V0bGluZTogbm9uZTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLm1pbmQtbWFwLXRleHQtZWRpdDpmb2N1cyB7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNjBhNWZhO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC4xKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLyog5rua5Yqo5p2h5qC35byPICovXHJcbiAgICAgIC5taW5kLW1hcC1jb250YWluZXIgOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgICAgICAgd2lkdGg6IDhweDtcclxuICAgICAgICBoZWlnaHQ6IDhweDtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLm1pbmQtbWFwLWNvbnRhaW5lciA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAubWluZC1tYXAtY29udGFpbmVyIDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC5taW5kLW1hcC1jb250YWluZXIgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvKiDliqDovb3liqjnlLsgKi9cclxuICAgICAgLm1pbmQtbWFwLWxvYWRpbmcge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgYmFja2dyb3VuZDogIzFhMWEyZTtcclxuICAgICAgICBjb2xvcjogI2NiZDVlMTtcclxuICAgICAgICBmb250LWZhbWlseTogXCJNaWNyb3NvZnQgWWFIZWlcIiwgXCJQaW5nRmFuZyBTQ1wiLCBcIkhlbHZldGljYSBOZXVlXCIsIEFyaWFsLCBzYW5zLXNlcmlmO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAubWluZC1tYXAtbG9hZGluZzo6YWZ0ZXIge1xyXG4gICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgIHdpZHRoOiAzMnB4O1xyXG4gICAgICAgIGhlaWdodDogMzJweDtcclxuICAgICAgICBtYXJnaW4tbGVmdDogOHB4O1xyXG4gICAgICAgIGJvcmRlcjogM3B4IHNvbGlkIHJnYmEoNTksIDEzMCwgMjQ2LCAwLjMpO1xyXG4gICAgICAgIGJvcmRlci10b3AtY29sb3I6ICM0YTkwZTI7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgIGFuaW1hdGlvbjogbWluZC1tYXAtc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIEBrZXlmcmFtZXMgbWluZC1tYXAtc3BpbiB7XHJcbiAgICAgICAgdG8ge1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIGA7XHJcbiAgICBcclxuICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoc3R5bGUpO1xyXG4gICAgY29uc29sZS5sb2coJ0N1c3RvbSBzdHlsZXMgaW5qZWN0ZWQnKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOmHjee9ruagt+W8j1xyXG4gICAqL1xyXG4gIHB1YmxpYyByZXNldFN0eWxlcygpOiB2b2lkIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIHRoaXMuY3VzdG9tU3R5bGVzID0ge307XHJcbiAgICAgIFxyXG4gICAgICAvLyDph43mlrDlupTnlKjlvZPliY3kuLvpophcclxuICAgICAgdGhpcy5hcHBseVRoZW1lKHRoaXMuY3VycmVudFRoZW1lKTtcclxuICAgICAgXHJcbiAgICAgIGNvbnNvbGUubG9nKCdTdHlsZXMgcmVzZXQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIFxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVzZXR0aW5nIHN0eWxlczonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDmm7TmlrDoioLngrnmoLflvI9cclxuICAgKi9cclxuICBwdWJsaWMgdXBkYXRlTm9kZVN0eWxlKG5vZGVMZXZlbDogJ3Jvb3QnIHwgJ3NlY29uZCcgfCAnbm9kZScgfCAnZ2VuZXJhbGl6YXRpb24nLCBzdHlsZXM6IFJlY29yZDxzdHJpbmcsIGFueT4pOiB2b2lkIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghdGhpcy5taW5kTWFwIHx8ICF0aGlzLm1pbmRNYXAudGhlbWVDb25maWcpIHJldHVybjtcclxuICAgICAgXHJcbiAgICAgIGNvbnN0IHZhbGlkYXRlZFN0eWxlcyA9IHRoaXMudmFsaWRhdGVTdHlsZXMoc3R5bGVzKTtcclxuICAgICAgdGhpcy5taW5kTWFwLnRoZW1lQ29uZmlnW25vZGVMZXZlbF0gPSB7IFxyXG4gICAgICAgIC4uLnRoaXMubWluZE1hcC50aGVtZUNvbmZpZ1tub2RlTGV2ZWxdLCBcclxuICAgICAgICAuLi52YWxpZGF0ZWRTdHlsZXMgXHJcbiAgICAgIH07XHJcbiAgICAgIFxyXG4gICAgICB0aGlzLm1pbmRNYXAucmVuZGVyKCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKGAke25vZGVMZXZlbH0gbm9kZSBzdHlsZSB1cGRhdGVkYCk7XHJcbiAgICAgIFxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgdXBkYXRpbmcgJHtub2RlTGV2ZWx9IG5vZGUgc3R5bGU6YCwgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6I635Y+W5b2T5YmN5qC35byP6YWN572uXHJcbiAgICovXHJcbiAgcHVibGljIGdldEN1cnJlbnRTdHlsZXMoKTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICB0aGVtZTogdGhpcy5jdXJyZW50VGhlbWUsXHJcbiAgICAgIGN1c3RvbVN0eWxlczogeyAuLi50aGlzLmN1c3RvbVN0eWxlcyB9LFxyXG4gICAgICB0aGVtZUNvbmZpZzogdGhpcy5taW5kTWFwPy50aGVtZUNvbmZpZyB8fCB7fVxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOmUgOavgeagt+W8j+euoeeQhuWZqFxyXG4gICAqL1xyXG4gIHB1YmxpYyBkZXN0cm95KCk6IHZvaWQge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g56e76Zmk5rOo5YWl55qE5qC35byPXHJcbiAgICAgIGNvbnN0IHN0eWxlRWxlbWVudCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtaW5kLW1hcC1jdXN0b20tc3R5bGVzJyk7XHJcbiAgICAgIGlmIChzdHlsZUVsZW1lbnQpIHtcclxuICAgICAgICBzdHlsZUVsZW1lbnQucmVtb3ZlKCk7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIOmHjee9rueKtuaAgVxyXG4gICAgICB0aGlzLm1pbmRNYXAgPSBudWxsO1xyXG4gICAgICB0aGlzLmN1c3RvbVN0eWxlcyA9IHt9O1xyXG4gICAgICB0aGlzLmlzQXBwbGllZCA9IGZhbHNlO1xyXG4gICAgICBcclxuICAgICAgY29uc29sZS5sb2coJ1N0eWxlTWFuYWdlciBkZXN0cm95ZWQnKTtcclxuICAgICAgXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZXN0cm95aW5nIHN0eWxlIG1hbmFnZXI6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8g5a+85Ye65Y2V5L6L5a6e5L6LXHJcbmV4cG9ydCBjb25zdCBzdHlsZU1hbmFnZXIgPSBuZXcgU3R5bGVNYW5hZ2VySW1wbCgpO1xyXG5leHBvcnQgdHlwZSB7IFN0eWxlTWFuYWdlciB9O1xyXG5leHBvcnQgZGVmYXVsdCBzdHlsZU1hbmFnZXI7Il0sIm5hbWVzIjpbImlzVmFsaWRDb2xvciIsInNhZmVFeGVjdXRlIiwiU3R5bGVNYW5hZ2VySW1wbCIsImluaXRpYWxpemUiLCJtaW5kTWFwIiwiYXBwbHlUaGVtZSIsImN1cnJlbnRUaGVtZSIsImluamVjdEN1c3RvbVN0eWxlcyIsImlzQXBwbGllZCIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsIm1pbmRNYXBFcnJvciIsIk1pbmRNYXBFcnJvciIsIk1pbmRNYXBFcnJvclR5cGUiLCJUSEVNRV9BUFBMSUNBVElPTl9GQUlMRUQiLCJFcnJvciIsIm1lc3NhZ2UiLCJ1bmRlZmluZWQiLCJ0aGVtZSIsIndhcm4iLCJhcHBseVRoZW1lSW50ZXJuYWwiLCJkYXJrVGhlbWVDb25maWciLCJnZXREYXJrVGhlbWVDb25maWciLCJzZXRUaGVtZSIsImFwcGx5VGhlbWVDb25maWciLCJiYWNrZ3JvdW5kQ29sb3IiLCJiYWNrZ3JvdW5kSW1hZ2UiLCJsaW5lQ29sb3IiLCJsaW5lV2lkdGgiLCJsaW5lU3R5bGUiLCJsaW5lRGFzaGFycmF5IiwibGluZU9wYWNpdHkiLCJyb290Iiwic2hhcGUiLCJmaWxsQ29sb3IiLCJib3JkZXJDb2xvciIsImJvcmRlcldpZHRoIiwiYm9yZGVyUmFkaXVzIiwiZm9udFNpemUiLCJmb250RmFtaWx5IiwiZm9udFdlaWdodCIsImNvbG9yIiwicGFkZGluZyIsIm1hcmdpbiIsIm9wYWNpdHkiLCJ0ZXh0RGVjb3JhdGlvbiIsImZvbnRTdHlsZSIsInNlY29uZCIsIm5vZGUiLCJnZW5lcmFsaXphdGlvbiIsImV4cGFuZEJ0blNpemUiLCJleHBhbmRCdG5TdHlsZSIsInN0cm9rZUNvbG9yIiwic3Ryb2tlV2lkdGgiLCJyYWRpdXMiLCJhc3NvY2lhdGl2ZUxpbmUiLCJzdHJva2VEYXNoYXJyYXkiLCJhY3RpdmVOb2RlU3R5bGUiLCJob3Zlck5vZGVTdHlsZSIsImNvbmZpZyIsIk9iamVjdCIsImtleXMiLCJmb3JFYWNoIiwia2V5IiwidGhlbWVDb25maWciLCJyZW5kZXIiLCJnZXRUaGVtZUNvbmZpZyIsImdldExpZ2h0VGhlbWVDb25maWciLCJhcHBseUN1c3RvbVN0eWxlcyIsInN0eWxlcyIsImN1c3RvbVN0eWxlcyIsInZhbGlkYXRlZFN0eWxlcyIsInZhbGlkYXRlU3R5bGVzIiwiYXBwbHlWYWxpZGF0ZWRTdHlsZXMiLCJ2YWx1ZSIsImluY2x1ZGVzIiwiaXNOYU4iLCJsZW5ndGgiLCJ1cGRhdGVDb25maWciLCJ0YXJnZXQiLCJzb3VyY2UiLCJBcnJheSIsImlzQXJyYXkiLCJzdHlsZUlkIiwiZG9jdW1lbnQiLCJnZXRFbGVtZW50QnlJZCIsInN0eWxlIiwiY3JlYXRlRWxlbWVudCIsImlkIiwidGV4dENvbnRlbnQiLCJoZWFkIiwiYXBwZW5kQ2hpbGQiLCJyZXNldFN0eWxlcyIsInVwZGF0ZU5vZGVTdHlsZSIsIm5vZGVMZXZlbCIsImdldEN1cnJlbnRTdHlsZXMiLCJkZXN0cm95Iiwic3R5bGVFbGVtZW50IiwicmVtb3ZlIiwic3R5bGVNYW5hZ2VyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ })

});