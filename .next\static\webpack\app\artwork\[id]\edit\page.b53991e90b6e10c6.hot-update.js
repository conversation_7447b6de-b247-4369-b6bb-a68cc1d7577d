"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n * \r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \nclass CoreManager {\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        try {\n            // 动态导入SimpleMindMap避免SSR问题\n            const { default: SimpleMindMap } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_simple-mind-map_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map */ \"(app-pages-browser)/./node_modules/simple-mind-map/index.js\"));\n            // 清理现有实例\n            if (this.mindMapInstance) {\n                this.mindMapInstance.destroy();\n            }\n            // 确保容器有明确的尺寸\n            const containerRect = this.config.container.getBoundingClientRect();\n            console.log(\"\\uD83D\\uDCD0 容器尺寸:\", {\n                width: containerRect.width,\n                height: containerRect.height\n            });\n            // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器\n            const finalConfig = {\n                ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                ...this.config.config,\n                el: this.config.container,\n                data: this.config.data,\n                readonly: this.config.readonly\n            };\n            // 创建实例\n            this.mindMapInstance = new SimpleMindMap(finalConfig);\n            this.isInitialized = true;\n            console.log(\"✅ SimpleMindMap实例初始化成功\");\n            return this.mindMapInstance;\n        } catch (error) {\n            console.error(\"❌ SimpleMindMap初始化失败:\", error);\n            throw new Error(\"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\"));\n        }\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            this.mindMapInstance.setData(data);\n            console.log(\"✅ 数据设置成功\");\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n            console.log(\"✅ 数据更新成功\");\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 容器尺寸变化后调整画布\r\n   */ resize() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API调整容器尺寸变化\n            this.mindMapInstance.resize();\n            console.log(\"✅ 画布尺寸调整完成\");\n        } catch (error) {\n            console.error(\"❌ 画布尺寸调整失败:\", error);\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n            console.log(\"✅ 画布适应完成\");\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n            console.log(\"✅ 视图重置完成\");\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 销毁实例\r\n   */ destroy() {\n        if (this.mindMapInstance) {\n            try {\n                this.mindMapInstance.destroy();\n                console.log(\"✅ SimpleMindMap实例销毁完成\");\n            } catch (error) {\n                console.error(\"❌ 销毁SimpleMindMap实例失败:\", error);\n            }\n        }\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9Db3JlTWFuYWdlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7Ozs7OztDQVFDLEdBRW9HO0FBUzlGLE1BQU1DO0lBU1g7O0dBRUMsR0FDRCxNQUFNQyxhQUE2QztRQUNqRCxJQUFJO1lBQ0YsMkJBQTJCO1lBQzNCLE1BQU0sRUFBRUMsU0FBU0MsYUFBYSxFQUFFLEdBQUcsTUFBTSwyT0FBTztZQUVoRCxTQUFTO1lBQ1QsSUFBSSxJQUFJLENBQUNDLGVBQWUsRUFBRTtnQkFDeEIsSUFBSSxDQUFDQSxlQUFlLENBQUNDLE9BQU87WUFDOUI7WUFFQSxhQUFhO1lBQ2IsTUFBTUMsZ0JBQWdCLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxTQUFTLENBQUNDLHFCQUFxQjtZQUNqRUMsUUFBUUMsR0FBRyxDQUFDLHNCQUFZO2dCQUN0QkMsT0FBT04sY0FBY00sS0FBSztnQkFDMUJDLFFBQVFQLGNBQWNPLE1BQU07WUFDOUI7WUFFQSwrQ0FBK0M7WUFDL0MsTUFBTUMsY0FBYztnQkFDbEIsR0FBR2YsMERBQXNCO2dCQUN6QixHQUFHLElBQUksQ0FBQ1EsTUFBTSxDQUFDQSxNQUFNO2dCQUNyQlEsSUFBSSxJQUFJLENBQUNSLE1BQU0sQ0FBQ0MsU0FBUztnQkFDekJRLE1BQU0sSUFBSSxDQUFDVCxNQUFNLENBQUNTLElBQUk7Z0JBQ3RCQyxVQUFVLElBQUksQ0FBQ1YsTUFBTSxDQUFDVSxRQUFRO1lBRWhDO1lBRUEsT0FBTztZQUNQLElBQUksQ0FBQ2IsZUFBZSxHQUFHLElBQUlELGNBQWNXO1lBQ3pDLElBQUksQ0FBQ0ksYUFBYSxHQUFHO1lBRXJCUixRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPLElBQUksQ0FBQ1AsZUFBZTtRQUU3QixFQUFFLE9BQU9lLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkMsTUFBTSxJQUFJQyxNQUFNLHVDQUFnRyxPQUF6REQsaUJBQWlCQyxRQUFRRCxNQUFNRSxPQUFPLEdBQUc7UUFDbEc7SUFDRjtJQUVBOztHQUVDLEdBQ0RDLGNBQTRDO1FBQzFDLE9BQU8sSUFBSSxDQUFDbEIsZUFBZTtJQUM3QjtJQUVBOztHQUVDLEdBQ0RtQixVQUFtQjtRQUNqQixPQUFPLElBQUksQ0FBQ0wsYUFBYSxJQUFJLElBQUksQ0FBQ2QsZUFBZSxLQUFLO0lBQ3hEO0lBRUE7O0dBRUMsR0FDRG9CLFFBQVFSLElBQWlCLEVBQVE7UUFDL0IsSUFBSSxDQUFDLElBQUksQ0FBQ1osZUFBZSxFQUFFO1lBQ3pCLE1BQU0sSUFBSWdCLE1BQU07UUFDbEI7UUFFQSxJQUFJO1lBQ0YsSUFBSSxDQUFDaEIsZUFBZSxDQUFDb0IsT0FBTyxDQUFDUjtZQUM3Qk4sUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPUSxPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyxhQUFhQTtZQUMzQixNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNETSxXQUFXVCxJQUFpQixFQUFRO1FBQ2xDLElBQUksQ0FBQyxJQUFJLENBQUNaLGVBQWUsRUFBRTtZQUN6QixNQUFNLElBQUlnQixNQUFNO1FBQ2xCO1FBRUEsSUFBSTtZQUNGLHlCQUF5QjtZQUN6QixJQUFJLE9BQU8sSUFBSSxDQUFDaEIsZUFBZSxDQUFDcUIsVUFBVSxLQUFLLFlBQVk7Z0JBQ3pELElBQUksQ0FBQ3JCLGVBQWUsQ0FBQ3FCLFVBQVUsQ0FBQ1Q7WUFDbEMsT0FBTztnQkFDTCxPQUFPO2dCQUNQLElBQUksQ0FBQ1osZUFBZSxDQUFDb0IsT0FBTyxDQUFDUjtZQUMvQjtZQUNBTixRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9RLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0RPLFVBQWlDO1lBQXpCQyxhQUFBQSxpRUFBYTtRQUNuQixJQUFJLENBQUMsSUFBSSxDQUFDdkIsZUFBZSxFQUFFO1lBQ3pCLE1BQU0sSUFBSWdCLE1BQU07UUFDbEI7UUFFQSxJQUFJO1lBQ0YsT0FBTyxJQUFJLENBQUNoQixlQUFlLENBQUNzQixPQUFPLENBQUNDO1FBQ3RDLEVBQUUsT0FBT1IsT0FBTztZQUNkVCxRQUFRUyxLQUFLLENBQUMsYUFBYUE7WUFDM0IsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRFMsU0FBZTtRQUNiLElBQUksQ0FBQyxJQUFJLENBQUN4QixlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLGtCQUFrQjtZQUNsQixJQUFJLENBQUNBLGVBQWUsQ0FBQ3dCLE1BQU07WUFDM0JsQixRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9RLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLGVBQWVBO1FBQy9CO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEVSxVQUFnQjtRQUNkLElBQUksQ0FBQyxJQUFJLENBQUN6QixlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLGNBQWM7WUFDZCxJQUFJLENBQUNBLGVBQWUsQ0FBQzBCLElBQUksQ0FBQ0MsR0FBRztZQUM3QnJCLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT1EsT0FBTztZQUNkVCxRQUFRUyxLQUFLLENBQUMsYUFBYUE7UUFDN0I7SUFDRjtJQUVBOztHQUVDLEdBQ0RhLFlBQWtCO1FBQ2hCLElBQUksQ0FBQyxJQUFJLENBQUM1QixlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLElBQUksQ0FBQ0EsZUFBZSxDQUFDMEIsSUFBSSxDQUFDRyxLQUFLO1lBQy9CdkIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPUSxPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyxhQUFhQTtRQUM3QjtJQUNGO0lBRUE7O0dBRUMsR0FDRGUsaUJBQWlCQyxPQUFlLEVBQUVDLE9BQWUsRUFBNEI7UUFDM0UsSUFBSSxDQUFDLElBQUksQ0FBQ2hDLGVBQWUsRUFBRTtZQUN6QixPQUFPO2dCQUFFaUMsR0FBR0Y7Z0JBQVNHLEdBQUdGO1lBQVE7UUFDbEM7UUFFQSxJQUFJO1lBQ0YsT0FBTyxJQUFJLENBQUNoQyxlQUFlLENBQUNtQyxLQUFLLENBQUNKLFNBQVNDO1FBQzdDLEVBQUUsT0FBT2pCLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE9BQU87Z0JBQUVrQixHQUFHRjtnQkFBU0csR0FBR0Y7WUFBUTtRQUNsQztJQUNGO0lBRUE7O0dBRUMsR0FDRC9CLFVBQWdCO1FBQ2QsSUFBSSxJQUFJLENBQUNELGVBQWUsRUFBRTtZQUN4QixJQUFJO2dCQUNGLElBQUksQ0FBQ0EsZUFBZSxDQUFDQyxPQUFPO2dCQUM1QkssUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPUSxPQUFPO2dCQUNkVCxRQUFRUyxLQUFLLENBQUMsMEJBQTBCQTtZQUMxQztRQUNGO1FBRUEsSUFBSSxDQUFDZixlQUFlLEdBQUc7UUFDdkIsSUFBSSxDQUFDYyxhQUFhLEdBQUc7SUFDdkI7SUFoTUFzQixZQUFZakMsTUFBeUIsQ0FBRTthQUovQkgsa0JBQWdEO2FBRWhEYyxnQkFBZ0I7UUFHdEIsSUFBSSxDQUFDWCxNQUFNLEdBQUdBO0lBQ2hCO0FBK0xGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9Db3JlTWFuYWdlci50cz8yNDU1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiBDb3JlTWFuYWdlciAtIOaguOW/g+euoeeQhuWZqFxyXG4gKiDotJ/otKNTaW1wbGVNaW5kTWFw5a6e5L6L55qE5Yib5bu644CB5Yid5aeL5YyW5ZKM5pWw5o2u566h55CGXHJcbiAqIFxyXG4gKiDogYzotKPvvJpcclxuICogLSBTaW1wbGVNaW5kTWFw5a6e5L6L55Sf5ZG95ZGo5pyf566h55CGXHJcbiAqIC0g5pWw5o2u6K6+572u5ZKM5pu05pawXHJcbiAqIC0g5Z+656GA6YWN572u566h55CGXHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgU2ltcGxlTWluZE1hcEluc3RhbmNlLCBNaW5kTWFwTm9kZSwgTWluZE1hcENvbmZpZywgREVGQVVMVF9NSU5ETUFQX0NPTkZJRyB9IGZyb20gJy4uL3R5cGVzJztcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ29yZU1hbmFnZXJDb25maWcge1xyXG4gIGNvbnRhaW5lcjogSFRNTEVsZW1lbnQ7XHJcbiAgZGF0YTogTWluZE1hcE5vZGU7XHJcbiAgcmVhZG9ubHk6IGJvb2xlYW47XHJcbiAgY29uZmlnOiBQYXJ0aWFsPE1pbmRNYXBDb25maWc+O1xyXG59XHJcblxyXG5leHBvcnQgY2xhc3MgQ29yZU1hbmFnZXIge1xyXG4gIHByaXZhdGUgbWluZE1hcEluc3RhbmNlOiBTaW1wbGVNaW5kTWFwSW5zdGFuY2UgfCBudWxsID0gbnVsbDtcclxuICBwcml2YXRlIGNvbmZpZzogQ29yZU1hbmFnZXJDb25maWc7XHJcbiAgcHJpdmF0ZSBpc0luaXRpYWxpemVkID0gZmFsc2U7XHJcblxyXG4gIGNvbnN0cnVjdG9yKGNvbmZpZzogQ29yZU1hbmFnZXJDb25maWcpIHtcclxuICAgIHRoaXMuY29uZmlnID0gY29uZmlnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5Yid5aeL5YyWU2ltcGxlTWluZE1hcOWunuS+i1xyXG4gICAqL1xyXG4gIGFzeW5jIGluaXRpYWxpemUoKTogUHJvbWlzZTxTaW1wbGVNaW5kTWFwSW5zdGFuY2U+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOWKqOaAgeWvvOWFpVNpbXBsZU1pbmRNYXDpgb/lhY1TU1Lpl67pophcclxuICAgICAgY29uc3QgeyBkZWZhdWx0OiBTaW1wbGVNaW5kTWFwIH0gPSBhd2FpdCBpbXBvcnQoJ3NpbXBsZS1taW5kLW1hcCcpO1xyXG5cclxuICAgICAgLy8g5riF55CG546w5pyJ5a6e5L6LXHJcbiAgICAgIGlmICh0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLmRlc3Ryb3koKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8g56Gu5L+d5a655Zmo5pyJ5piO56Gu55qE5bC65a+4XHJcbiAgICAgIGNvbnN0IGNvbnRhaW5lclJlY3QgPSB0aGlzLmNvbmZpZy5jb250YWluZXIuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OQIOWuueWZqOWwuuWvuDonLCB7IFxyXG4gICAgICAgIHdpZHRoOiBjb250YWluZXJSZWN0LndpZHRoLCBcclxuICAgICAgICBoZWlnaHQ6IGNvbnRhaW5lclJlY3QuaGVpZ2h0IFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIOWQiOW5tumFjee9riAtIOS4jeiuvue9ruWbuuWumndpZHRoL2hlaWdodO+8jOiuqVNpbXBsZU1pbmRNYXDoh6rpgILlupTlrrnlmahcclxuICAgICAgY29uc3QgZmluYWxDb25maWcgPSB7XHJcbiAgICAgICAgLi4uREVGQVVMVF9NSU5ETUFQX0NPTkZJRyxcclxuICAgICAgICAuLi50aGlzLmNvbmZpZy5jb25maWcsXHJcbiAgICAgICAgZWw6IHRoaXMuY29uZmlnLmNvbnRhaW5lcixcclxuICAgICAgICBkYXRhOiB0aGlzLmNvbmZpZy5kYXRhLFxyXG4gICAgICAgIHJlYWRvbmx5OiB0aGlzLmNvbmZpZy5yZWFkb25seVxyXG4gICAgICAgIC8vIOenu+mZpOWbuuWumueahHdpZHRoL2hlaWdodOiuvue9ru+8jOiuqVNpbXBsZU1pbmRNYXDoh6rliqjpgILlupTlrrnlmajlsLrlr7hcclxuICAgICAgfTtcclxuXHJcbiAgICAgIC8vIOWIm+W7uuWunuS+i1xyXG4gICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZSA9IG5ldyBTaW1wbGVNaW5kTWFwKGZpbmFsQ29uZmlnKSBhcyBTaW1wbGVNaW5kTWFwSW5zdGFuY2U7XHJcbiAgICAgIHRoaXMuaXNJbml0aWFsaXplZCA9IHRydWU7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFNpbXBsZU1pbmRNYXDlrp7kvovliJ3lp4vljJbmiJDlip8nKTtcclxuICAgICAgcmV0dXJuIHRoaXMubWluZE1hcEluc3RhbmNlO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBTaW1wbGVNaW5kTWFw5Yid5aeL5YyW5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gaW5pdGlhbGl6ZSBTaW1wbGVNaW5kTWFwOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6I635Y+WU2ltcGxlTWluZE1hcOWunuS+i1xyXG4gICAqL1xyXG4gIGdldEluc3RhbmNlKCk6IFNpbXBsZU1pbmRNYXBJbnN0YW5jZSB8IG51bGwge1xyXG4gICAgcmV0dXJuIHRoaXMubWluZE1hcEluc3RhbmNlO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5qOA5p+l5piv5ZCm5bey5Yid5aeL5YyWXHJcbiAgICovXHJcbiAgaXNSZWFkeSgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLmlzSW5pdGlhbGl6ZWQgJiYgdGhpcy5taW5kTWFwSW5zdGFuY2UgIT09IG51bGw7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDorr7nva7mlbDmja5cclxuICAgKi9cclxuICBzZXREYXRhKGRhdGE6IE1pbmRNYXBOb2RlKTogdm9pZCB7XHJcbiAgICBpZiAoIXRoaXMubWluZE1hcEluc3RhbmNlKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignTWluZE1hcCBpbnN0YW5jZSBub3QgaW5pdGlhbGl6ZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5zZXREYXRhKGRhdGEpO1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIOaVsOaNruiuvue9ruaIkOWKnycpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIOaVsOaNruiuvue9ruWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5pu05paw5pWw5o2u77yI5oCn6IO95LyY5YyW54mI5pys77yJXHJcbiAgICovXHJcbiAgdXBkYXRlRGF0YShkYXRhOiBNaW5kTWFwTm9kZSk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ01pbmRNYXAgaW5zdGFuY2Ugbm90IGluaXRpYWxpemVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5L2/55So5a6Y5pa555qEdXBkYXRlRGF0YeaWueazle+8jOaAp+iDveabtOWlvVxyXG4gICAgICBpZiAodHlwZW9mIHRoaXMubWluZE1hcEluc3RhbmNlLnVwZGF0ZURhdGEgPT09ICdmdW5jdGlvbicpIHtcclxuICAgICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS51cGRhdGVEYXRhKGRhdGEpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIC8vIOmZjee6p+aWueahiFxyXG4gICAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLnNldERhdGEoZGF0YSk7XHJcbiAgICAgIH1cclxuICAgICAgY29uc29sZS5sb2coJ+KchSDmlbDmja7mm7TmlrDmiJDlip8nKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDmlbDmja7mm7TmlrDlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOiOt+WPluaVsOaNrlxyXG4gICAqL1xyXG4gIGdldERhdGEod2l0aENvbmZpZyA9IGZhbHNlKTogYW55IHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwSW5zdGFuY2UpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdNaW5kTWFwIGluc3RhbmNlIG5vdCBpbml0aWFsaXplZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHJldHVybiB0aGlzLm1pbmRNYXBJbnN0YW5jZS5nZXREYXRhKHdpdGhDb25maWcpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluaVsOaNruWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5a655Zmo5bC65a+45Y+Y5YyW5ZCO6LCD5pW055S75biDXHJcbiAgICovXHJcbiAgcmVzaXplKCk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOS9v+eUqOWumOaWuUFQSeiwg+aVtOWuueWZqOWwuuWvuOWPmOWMllxyXG4gICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5yZXNpemUoKTtcclxuICAgICAgY29uc29sZS5sb2coJ+KchSDnlLvluIPlsLrlr7josIPmlbTlrozmiJAnKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDnlLvluIPlsLrlr7josIPmlbTlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6YCC5bqU55S75biD5aSn5bCPXHJcbiAgICovXHJcbiAgZml0VmlldygpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwSW5zdGFuY2UpIHJldHVybjtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDkvb/nlKjlrpjmlrlBUEnpgILlupTnlLvluINcclxuICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2Uudmlldy5maXQoKTtcclxuICAgICAgY29uc29sZS5sb2coJ+KchSDnlLvluIPpgILlupTlrozmiJAnKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDnlLvluIPpgILlupTlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6YeN572u6KeG5Zu+XHJcbiAgICovXHJcbiAgcmVzZXRWaWV3KCk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLnZpZXcucmVzZXQoKTtcclxuICAgICAgY29uc29sZS5sb2coJ+KchSDop4blm77ph43nva7lrozmiJAnKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDop4blm77ph43nva7lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5Z2Q5qCH6L2s5o2iXHJcbiAgICovXHJcbiAgdG9DYW52YXNQb3NpdGlvbihzY3JlZW5YOiBudW1iZXIsIHNjcmVlblk6IG51bWJlcik6IHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfSB7XHJcbiAgICBpZiAoIXRoaXMubWluZE1hcEluc3RhbmNlKSB7XHJcbiAgICAgIHJldHVybiB7IHg6IHNjcmVlblgsIHk6IHNjcmVlblkgfTtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICByZXR1cm4gdGhpcy5taW5kTWFwSW5zdGFuY2UudG9Qb3Moc2NyZWVuWCwgc2NyZWVuWSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Z2Q5qCH6L2s5o2i5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIHsgeDogc2NyZWVuWCwgeTogc2NyZWVuWSB9O1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6ZSA5q+B5a6e5L6LXHJcbiAgICovXHJcbiAgZGVzdHJveSgpOiB2b2lkIHtcclxuICAgIGlmICh0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLmRlc3Ryb3koKTtcclxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFNpbXBsZU1pbmRNYXDlrp7kvovplIDmr4HlrozmiJAnKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6ZSA5q+BU2ltcGxlTWluZE1hcOWunuS+i+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZSA9IG51bGw7XHJcbiAgICB0aGlzLmlzSW5pdGlhbGl6ZWQgPSBmYWxzZTtcclxuICB9XHJcbn0iXSwibmFtZXMiOlsiREVGQVVMVF9NSU5ETUFQX0NPTkZJRyIsIkNvcmVNYW5hZ2VyIiwiaW5pdGlhbGl6ZSIsImRlZmF1bHQiLCJTaW1wbGVNaW5kTWFwIiwibWluZE1hcEluc3RhbmNlIiwiZGVzdHJveSIsImNvbnRhaW5lclJlY3QiLCJjb25maWciLCJjb250YWluZXIiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJjb25zb2xlIiwibG9nIiwid2lkdGgiLCJoZWlnaHQiLCJmaW5hbENvbmZpZyIsImVsIiwiZGF0YSIsInJlYWRvbmx5IiwiaXNJbml0aWFsaXplZCIsImVycm9yIiwiRXJyb3IiLCJtZXNzYWdlIiwiZ2V0SW5zdGFuY2UiLCJpc1JlYWR5Iiwic2V0RGF0YSIsInVwZGF0ZURhdGEiLCJnZXREYXRhIiwid2l0aENvbmZpZyIsInJlc2l6ZSIsImZpdFZpZXciLCJ2aWV3IiwiZml0IiwicmVzZXRWaWV3IiwicmVzZXQiLCJ0b0NhbnZhc1Bvc2l0aW9uIiwic2NyZWVuWCIsInNjcmVlblkiLCJ4IiwieSIsInRvUG9zIiwiY29uc3RydWN0b3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ })

});