"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = (param)=>{\n    let { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, className = \"\", config = {} } = param;\n    _s();\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!containerRef.current) {\n            setRenderError(\"Container element not found\");\n            return;\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange,\n                onRenderComplete\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"Failed to initialize MindMap:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _selectionManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap();\n            }, 100);\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeMindMap,\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width,\n            height,\n            background: \"#1a1a1a\",\n            border: \"1px solid var(--color-primary-green, #8FBC8F)\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 2px 8px rgba(143, 188, 143, 0.3)\"\n        },\n        children: !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: \"absolute\",\n                top: \"10px\",\n                right: \"10px\",\n                background: \"rgba(143, 188, 143, 0.9)\",\n                color: \"#fff\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontSize: \"12px\",\n                zIndex: 100,\n                pointerEvents: \"none\",\n                fontFamily: \"var(--font-family-handwritten)\"\n            },\n            children: \"\\uD83D\\uDCA1 右键长按可框选多个节点\"\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 289,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MindMapRenderer, \"WsC+8anm46mIX4UTf2kk6BEDs5Y=\");\n_c = MindMapRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c;\n$RefreshReg$(_c, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});