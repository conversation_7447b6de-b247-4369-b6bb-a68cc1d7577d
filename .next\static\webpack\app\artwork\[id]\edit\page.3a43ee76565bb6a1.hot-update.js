"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts":
/*!*******************************************************!*\
  !*** ./src/components/MindMapRenderer/types/index.ts ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_MINDMAP_CONFIG: function() { return /* binding */ DEFAULT_MINDMAP_CONFIG; },\n/* harmony export */   DEFAULT_SELECTION_CONFIG: function() { return /* binding */ DEFAULT_SELECTION_CONFIG; }\n/* harmony export */ });\n/**\r\n * TypeScript类型定义\r\n * 为SimpleMindMap组件提供完整的类型支持\r\n */ // 基础几何类型\n// 默认配置\nconst DEFAULT_SELECTION_CONFIG = {\n    longPressDelay: 500,\n    moveThreshold: 8,\n    selectionBoxStyle: {\n        strokeColor: \"#8FBC8F\",\n        strokeWidth: 2,\n        fillColor: \"#8FBC8F\",\n        fillOpacity: 0.15\n    },\n    debug: false\n};\nconst DEFAULT_MINDMAP_CONFIG = {\n    readonly: false,\n    // 移除默认主题设置，让StyleManager控制主题\n    layout: \"logicalStructure\",\n    enableFreeDrag: false,\n    enableCtrlKeyNodeSelection: true,\n    enableAutoEnterTextEditWhenKeydown: true,\n    isEndNodeTextEditOnClickOuter: true,\n    maxHistoryCount: 100,\n    maxNodeCacheCount: 200,\n    fitPadding: 50,\n    mouseScaleCenterUseMousePosition: true\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\n"));

/***/ })

});